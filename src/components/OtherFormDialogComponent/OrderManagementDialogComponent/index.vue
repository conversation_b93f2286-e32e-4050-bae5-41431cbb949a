<!--
 * @Author: llm
 * @Date: 2025-03-19 14:25:58
 * @LastEditors: llm
 * @LastEditTime: 2025-05-12 11:50:11
 * @Description:
-->
<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      v-model="state.dialogVisible.visible"
      :title="state.dialogVisible.title"
      width="1000px"
      :draggable="true"
      @closed="cancelForm(formRef)"
    >
      <el-form :model="state.formData" :rules="rules" ref="formRef" label-position="right" label-width="110px">
        <el-row :gutter="12">
          <!-- <el-col :span="8">
            <el-form-item prop="importType" label="订单/计划">
              <el-select :disabled="state.origin === 'edit'" v-model="state.formData.importType" :clearable="true" placeholder="请选择" :filterable="true">
                <el-option :label="option.label" :value="option.label" v-for="option in importTypeOptions"></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="8">
            <el-form-item prop="businessType" label="业务类型">
              <el-select :disabled="state.origin === 'edit'" v-model="state.formData.businessType" :clearable="true" placeholder="请选择" :filterable="true">
                <el-option :label="option.label" :value="option.label" v-for="option in businessTypeOptions"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8"> </el-col>
          <el-col :span="8">
            <el-form-item prop="childCompanyId" label="所属基地" :required="true">
              <el-tree-select
                :disabled="state.origin === 'edit'"
                v-model="state.formData.childCompanyId"
                default-expand-all
                filterable
                check-strictly
                style="width: 100%"
                :multiple="false"
                placeholder="请选择所属基地"
                :data="childCompanyIdOptions"
                :props="{ value: 'value', label: 'label' }"
                clearable
                :style="{ width: '100%' }"
              >
                <template #default="{ data }">{{ data.label }}</template>
              </el-tree-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="customerId" label="客户名称" :required="true">
              <el-select :disabled="state.origin === 'edit'" v-model="state.formData.customerId" :clearable="true" placeholder="请选择" :filterable="true">
                <el-option :label="option.label" :value="option.value" v-for="option in customerIdOptions"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="orderType" label="订单类型" :required="true">
              <el-select
                @change="handleOrderTypeChange"
                :disabled="state.origin === 'edit'"
                v-model="state.formData.orderType"
                :clearable="true"
                placeholder="请选择"
                :filterable="true"
              >
                <el-option :label="option.label" :value="option.value" v-for="option in orderTypeOptions"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="orderNo" label="订单号">
              <el-input v-model="state.formData.orderNo" placeholder="请输入"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="orderIssueDatetime" label="计划下达时间">
              <el-date-picker
                style="width: 60%"
                :disabled="state.origin === 'edit'"
                value-format="YYYY-MM-DD HH:mm:ss"
                v-model="state.formData.orderIssueDatetime"
                type="datetime"
                placeholder="日期"
              >
              </el-date-picker>
              <el-button style="margin-left: 5px" size="small" type="primary" @click="Manual('Scheduledrelease')">手动输入</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="vin" label="VIN" :required="true">
              <el-input :disabled="state.origin === 'edit'" v-model="state.formData.vin" placeholder="请输入"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="twicePlan" label="是否二次计划">
              <el-select :disabled="state.origin === 'edit'" v-model="state.formData.twicePlan" :clearable="true" placeholder="请选择" :filterable="true">
                <el-option :label="option.label" :value="option.value" v-for="option in twicePlanOptions"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="expectedLoadTime" label="预计装车日期">
              <el-date-picker
                style="width: 60%"
                value-format="YYYY-MM-DD HH:mm:ss"
                v-model="state.formData.expectedLoadTime"
                type="datetime"
                :clearable="true"
                placeholder="日期"
              >
              </el-date-picker>
              <el-button size="small" style="margin-left: 5px" type="primary" @click="Manual('Expectedloading')"> 手动输入 </el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="predictDeliveryDatetime" label="预计交车日期">
              <el-date-picker
                style="width: 60%"
                value-format="YYYY-MM-DD HH:mm:ss"
                v-model="state.formData.predictDeliveryDatetime"
                type="datetime"
                :clearable="true"
                placeholder="日期"
              >
              </el-date-picker>
              <el-button size="small" style="margin-left: 5px" type="primary" @click="Manual('Deliveryisexpected')"> 手动输入 </el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="startFenceId" label="起点" :required="true">
              <el-select
                :disabled="state.origin === 'edit'"
                v-model="state.formData.startFenceId"
                style="width: 100%"
                remote-show-suffix
                :multiple="false"
                filterable
                remote
                reserve-keyword
                placeholder="模糊搜索"
                @change="(e: any) => selectFence(e, 'startFenceId')"
                :remote-method="(query: string) => remoteSelectMethod(query, 'startFenceId')"
              >
                <el-option v-for="i in startFenceIdOptions || []" :key="i.value" :label="i.label" :value="i.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="endFenceId" label="终点" :required="true">
              <el-select
                :disabled="state.origin === 'edit'"
                v-model="state.formData.endFenceId"
                style="width: 100%"
                remote-show-suffix
                :multiple="false"
                filterable
                remote
                reserve-keyword
                placeholder="模糊搜索"
                @change="(e: any) => selectFence(e, 'endFenceId')"
                :remote-method="(query: string) => remoteSelectMethod(query, 'endFenceId')"
              >
                <el-option v-for="i in endFenceIdOptions || []" :key="i.value" :label="i.label" :value="i.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="customerOrderNo" label="客户单号">
              <el-input v-model="state.formData.customerOrderNo" placeholder="请输入"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="brandName" label="品牌">
              <el-input :disabled="state.origin === 'edit'" v-model="state.formData.brandName" placeholder="请输入"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="modelName" label="车型">
              <el-input :disabled="state.origin === 'edit'" v-model="state.formData.modelName" placeholder="请输入"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="dealerName" label="经销商名称">
              <el-input v-model="state.formData.dealerName" placeholder="请输入"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="dealerAddress" label="经销商地址">
              <el-input v-model="state.formData.dealerAddress" placeholder="请输入"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="pickUpPointId" label="提车点">
              <el-select
                v-model="state.formData.pickUpPointId"
                style="width: 100%"
                remote-show-suffix
                :multiple="false"
                filterable
                remote
                reserve-keyword
                placeholder="模糊搜索"
                @change="(e: any) => selectFence(e, 'pickUpPointId')"
                :remote-method="(query: string) => remoteSelectMethod(query, 'pickUpPointId')"
              >
                <el-option v-for="i in pickUpPointIdOptions || []" :key="i.value" :label="i.label" :value="i.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="pickUpPointAddress" label="提车地址">
              <el-input v-model="state.formData.pickUpPointAddress" placeholder="请输入" :disabled="true"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="dropUpPointId" label="交车点" :required="true">
              <el-select
                v-model="state.formData.dropUpPointId"
                style="width: 100%"
                remote-show-suffix
                :multiple="false"
                filterable
                remote
                reserve-keyword
                placeholder="模糊搜索"
                @change="(e: any) => selectFence(e, 'dropUpPointId')"
                :remote-method="(query: string) => remoteSelectMethod(query, 'dropUpPointId')"
              >
                <el-option v-for="i in dropUpPointIdOptions || []" :key="i.value" :label="i.label" :value="i.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="dropUpPointAddress" label="交车地址" :required="true">
              <el-input v-model="state.formData.dropUpPointAddress" placeholder="请输入" :disabled="true"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="state.formData.orderType === '零散订单'">
            <el-form-item prop="paymentMethod" label="付款方式" :required="false">
              <el-select v-model="state.formData.paymentMethod" :clearable="true" placeholder="请选择" :filterable="true">
                <el-option :label="option.label" :value="option.value" v-for="option in paymentMethodOptions"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="state.formData.orderType === '零散订单'">
            <el-form-item prop="amountReceivable" label="金额" :required="false">
              <el-input-number style="width: 100%" v-model="state.formData.amountReceivable" :precision="2" :min="0" placeholder="请输入"> </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="state.formData.orderType === '零散订单'">
            <el-form-item prop="collectUserName" label="收款负责人" :required="false">
              <el-input v-model="state.formData.collectUserName" placeholder="请输入"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="state.formData.orderType === '零散订单'">
            <el-form-item prop="whetherDriverCollect" label="是否司机收款" label-width="110" :required="true">
              <el-select
                :disabled="state.origin === 'edit'"
                v-model="state.formData.whetherDriverCollect"
                :clearable="true"
                placeholder="请选择"
                :filterable="true"
              >
                <el-option :label="option.label" :value="option.value" v-for="option in whetherDriverCollectOptions"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="state.formData.orderType === '零散订单'">
            <el-form-item prop="haveDeliverySlip" label="有无交接单" :required="true">
              <el-select
                :disabled="state.origin === 'edit'"
                v-model="state.formData.haveDeliverySlip"
                :clearable="true"
                placeholder="请选择"
                :filterable="true"
              >
                <el-option :label="option.label" :value="option.value" v-for="option in haveDeliverySlipOptions"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="state.formData.orderType === '零散订单'">
            <el-form-item prop="planRefundDate" label="计划回款日期" label-width="110" :required="true">
              <el-date-picker
                :disabled="state.origin === 'edit'"
                value-format="YYYY-MM-DD"
                v-model="state.formData.planRefundDate"
                type="date"
                :clearable="true"
                placeholder="日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="state.formData.orderType === '零散订单'">
            <el-form-item prop="twicePlan" label="下付单独结算">
              <el-select
                :disabled="state.origin === 'edit'"
                v-model="state.formData.downPaymentSeparateSettlement"
                :clearable="true"
                placeholder="请选择"
                :filterable="true"
              >
                <el-option :label="option.label" :value="option.value" v-for="option in downPaymentSeparateSettlementOptions"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="state.formData.downPaymentSeparateSettlement === '是'">
            <el-form-item prop="downPaymentAmount" label="下付金额">
              <el-input-number style="width: 100%" v-model="state.formData.downPaymentAmount" :precision="2" :min="0" placeholder="请输入"> </el-input-number>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item prop="remark" label="备注">
              <el-input v-model="state.formData.remark" type="textarea" placeholder="请输入"> </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="cancelForm(formRef)">取消</el-button>
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
      </template>
    </el-dialog>
    <AddGeoFenceFormDialogComponent ref="addGeoFenceFormDialogComponent" @geoFenceAddSuccess="geoFenceAddSuccess" @geoFenceAddClose="geoFenceAddClose" />

    <el-dialog v-model="TimedialogVisible" title="手动输入" width="500">
      <el-form-item label="手动输入时间" label-width="100px">
        <el-input v-model="ManualTime" />
      </el-form-item>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="TimedialogVisible = false">取消</el-button>
          <el-button type="primary" @click="enter"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import { ref } from 'vue'
  import type { FormInstance } from 'element-plus'
  import { FleetOrderVO } from '@/api/outboundMaintenanceManagement/types'
  import {
    getCommonOptionBusinessSelectApi,
    getCommonOptionDistributeSelectApi,
    getDepartmentTreeOptionApi,
    getFleetOrderAddCheckApi,
    getFleetOrderDetailApi,
    getFleetOrderDropUpCheckApi,
    getFleetOrderPickUpCheckApi,
    getFleetOutFleetBusinessCustomerSelectOptionApi,
    postFleetOrderApi,
    putFleetOrderDetailApi,
    getOutFleetBusinessTypeEntitySelectOptionApi,
  } from '@/api/outboundMaintenanceManagement'
  import AddGeoFenceFormDialogComponent from '@/components/AddGeoFenceFormDialogComponent/index.vue'
  import { ElMessageBox } from 'element-plus'
  const emit = defineEmits(['refresh'])
  const addGeoFenceFormDialogComponent = ref()
  /**
   * 二级弹窗
   */
  const searchDialog = reactive<DialogOption>({
    visible: false,
  })

  let ManualTime = ref('')

  const enter = () => {
    if (distinguish.value === 'Scheduledrelease') {
      state.formData.orderIssueDatetime = formatDateToYYYYMMDD(ManualTime.value)
    } else if (distinguish.value === 'Expectedloading') {
      state.formData.expectedLoadTime = formatDateToYYYYMMDD(ManualTime.value)
    } else if (distinguish.value === 'Deliveryisexpected') {
      state.formData.predictDeliveryDatetime = formatDateToYYYYMMDD(ManualTime.value)
    }
    TimedialogVisible.value = false
  }

  function formatDateToYYYYMMDD(input: string) {
    let date
    let hasTime = false
    let hours = '00',
      minutes = '00',
      seconds = '00'

    // 1. 处理中文日期格式（可能含时间）
    if (input.includes('年')) {
      // 匹配格式：2025年6月7日 或 2025年6月7日11:58:33
      const cnPattern = /(\d+)年(\d+)月(\d+)日(?:(\d+):(\d+)(?::(\d+))?)?/
      const match = input.match(cnPattern)

      if (!match) throw new Error('Invalid Chinese date format')

      const [, year, month, day, h, m, s] = match
      date = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`)

      // 如果有时分秒
      if (h) {
        hasTime = true
        hours = h.padStart(2, '0')
        minutes = m.padStart(2, '0')
        seconds = (s || '00').padStart(2, '0')
      }
    }
    // 2. 处理特殊格式 YYYY-MM-DD-HH:mm
    else if (/^\d{4}-\d{2}-\d{2}-\d{2}:\d{2}$/.test(input)) {
          const [datePart, timePart] = input.split('-').slice(-2)
          const dateStr = input.split('-').slice(0, 3).join('-')
          date = new Date(dateStr)
          hasTime = true
          ;[hours, minutes] = timePart.split(':')
        }
    // 2. 处理其他格式（原逻辑）
    else {
      if (input.includes('-')) {
        date = new Date(input)
      } else if (input.includes('/')) {
        date = new Date(input.replace(/\//g, '-'))
      } else {
        date = new Date(input)
      }

      // 检查原始输入是否包含时间部分
      hasTime = /(\d{1,2}:\d{1,2}(:\d{1,2})?)/.test(input)
      if (hasTime) {
        hours = String(date.getHours()).padStart(2, '0')
        minutes = String(date.getMinutes()).padStart(2, '0')
        seconds = String(date.getSeconds()).padStart(2, '0')
      }
    }

    // 验证日期有效性
    if (isNaN(date.getTime())) throw new Error('Invalid date')

    // 统一格式化
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  // 定义一个定时器变量
  let timer: ReturnType<typeof setTimeout> | null = null

  // 清除定时器函数
  const clearTimer = () => {
    if (timer) {
      clearTimeout(timer)
      timer = null
    }
  }

  // 定义一个变量来控制手动输入的弹窗是否可见

  const TimedialogVisible = ref(false)

  const state = reactive({
    origin: 'add', //add:新增，edit:编辑
    dialogVisible: {
      visible: false,
      title: '',
    },
    formData: {
      id: undefined,
      childCompanyId: '',
      customerId: '',
      orderType: '合同订单',
      orderNo: '',
      orderIssueDatetime: '',
      vin: '',
      twicePlan: 0,
      expectedLoadTime: '',
      predictDeliveryDatetime: '',
      startFenceId: '',
      endFenceId: '',
      customerOrderNo: '',
      brandName: '',
      modelName: '',
      dealerName: '',
      dealerAddress: '',
      pickUpPointId: '',
      pickUpPointAddress: '',
      dropUpPointId: '',
      dropUpPointAddress: '',
      paymentMethod: '',
      amountReceivable: 0,
      collectUserName: '',
      whetherDriverCollect: 0,
      haveDeliverySlip: 0,
      planRefundDate: '',
      remark: '',
      businessType: '',
      downPaymentSeparateSettlement: '否',
      downPaymentAmount: 0,
      // importType: '',
    } as FleetOrderVO,
    geoFenceFormData: {
      address: '',
      coordinate: '',
    },
    addressFuzzySearchParams: {
      label: 'name',
      dataSource: '围栏',
      value: 'id',
      fuzzy: true,
      keyword: '',
      link: '开启', //设置开启后当搜索不到时，返回-10000，弹出选择地址弹窗，让用户新增围栏
    },
  })

  const formRef = ref<FormInstance>()
  const rules = ref({
    childCompanyId: [{ required: true, message: '请选择', trigger: 'change' }],
    customerId: [{ required: true, message: '请选择', trigger: 'change' }],
    orderType: [{ required: true, message: '请选择', trigger: 'change' }],
    vin: [{ required: true, message: '请输入', trigger: 'blur' }],
    startFenceId: [{ required: true, message: '请选择', trigger: 'change' }],
    endFenceId: [{ required: true, message: '请选择', trigger: 'change' }],
    dropUpPointId: [{ required: true, message: '请选择', trigger: 'change' }],
    dropUpPointAddress: [{ required: true, message: '请选择交车点', trigger: 'change' }],
    paymentMethod: [{ required: true, message: '请选择', trigger: 'change' }],
    amountReceivable: [{ required: true, message: '请输入', trigger: 'blur' }],
    collectUserName: [{ required: true, message: '请输入', trigger: 'blur' }],
    whetherDriverCollect: [{ required: true, message: '请选择', trigger: 'change' }],
    haveDeliverySlip: [{ required: true, message: '请选择', trigger: 'change' }],
    planRefundDate: [{ required: true, message: '请选择', trigger: 'change' }],
    downPaymentAmount: [{ required: false, message: '请输入', trigger: 'blur' }],
    importType: [{ required: true, message: '请选择', trigger: 'change' }],
  })
  const childCompanyIdOptions = ref<SelectOptions[]>([])
  const customerIdOptions = ref<SelectOptions[]>([])
  const orderTypeOptions = ref<SelectOptions[]>([])
  const businessTypeOptions = ref<SelectOptions[]>([])
  const importTypeOptions = ref<SelectOptions[]>([
    {
      label: '订单',
      value: '订单',
    },
    {
      label: '计划',
      value: '计划',
    },
  ])
  const twicePlanOptions = ref<SelectOptions[]>([
    {
      label: '是',
      value: 1,
    },
    {
      label: '否',
      value: 0,
    },
  ])
  const downPaymentSeparateSettlementOptions = ref<SelectOptions[]>([
    {
      label: '是',
      value: '是',
    },
    {
      label: '否',
      value: '否',
    },
  ])
  const startFenceIdOptions = ref<SelectOptions[]>([])
  const endFenceIdOptions = ref<SelectOptions[]>([])
  const pickUpPointIdOptions = ref<SelectOptions[]>([])
  const dropUpPointIdOptions = ref<SelectOptions[]>([])
  const paymentMethodOptions = ref<SelectOptions[]>([
    {
      label: '现金',
      value: '现金',
    },
    {
      label: '汇款',
      value: '汇款',
    },
  ])
  const whetherDriverCollectOptions = ref<SelectOptions[]>([
    {
      label: '是',
      value: 1,
    },
    {
      label: '否',
      value: 0,
    },
  ])
  const haveDeliverySlipOptions = ref([
    {
      label: '是',
      value: 1,
    },
    {
      label: '否',
      value: 0,
    },
  ])

  let distinguish = ref('')
  const Manual = (str: string) => {
    if (str === 'Scheduledrelease') {
      ManualTime.value = state.formData.orderIssueDatetime
    } else if (str === 'Expectedloading') {
      ManualTime.value = state.formData.expectedLoadTime
    } else if (str === 'Deliveryisexpected') {
      ManualTime.value = state.formData.predictDeliveryDatetime
    }
    distinguish.value = str
    TimedialogVisible.value = true
  }
  watch(
    () => state.formData.amountReceivable,
    (newVal: number) => {
      if (Number(newVal) < 0) {
        state.formData.amountReceivable = 0
      }
    },
  )
  watch(
    () => state.dialogVisible.visible,
    () => {
      getDepartmentTreeOption()
      getChildCompanyIdOptions()
      getCustomerIdOptions()
      getOrderTypeOptions()
      getBusinessTypeOptions()
    },
  )
  //所属基地下拉
  const getDepartmentTreeOption = async () => {
    const { data } = await getDepartmentTreeOptionApi({ enable: true })
    childCompanyIdOptions.value = data as SelectOptions[]
  }
  //所属基地默认下拉
  const getChildCompanyIdOptions = async () => {
    const { data } = await getFleetOrderAddCheckApi({})
    state.formData.childCompanyId = data.childCompanyId
  }
  //客户名称下拉
  const getCustomerIdOptions = async () => {
    const params = {
      label: 'name',
      dataSource: '车队-客户',
      value: 'id',
    }
    const { data } = await getFleetOutFleetBusinessCustomerSelectOptionApi(params)
    customerIdOptions.value = data as SelectOptions[]
  }
  //订单类型下拉
  const getOrderTypeOptions = async () => {
    const params = {
      selectType: 'fleetOutOrderType',
      simple: true,
      needAll: false,
      value: 'name',
    }
    const { data } = await getCommonOptionDistributeSelectApi(params)
    orderTypeOptions.value = data as SelectOptions[]
  }
  const getBusinessTypeOptions = async () => {
    const { data } = await getOutFleetBusinessTypeEntitySelectOptionApi()
    businessTypeOptions.value = data as SelectOptions[]
  }
  const selectFence = async (e: any, key: string) => {
    if (key === 'pickUpPointId') {
      if (e === '-10000') {
        addGeoFenceFormDialogComponent.value.dialogVisible = true
        addGeoFenceFormDialogComponent.value.state.key = key
      } else {
        //提车地查询提车地址
        const { data } = await getFleetOrderPickUpCheckApi({ pickUpPointId: e })
        state.formData.pickUpPointAddress = data.pickUpPointAddress
      }
    } else if (key === 'dropUpPointId') {
      if (e === '-10000') {
        addGeoFenceFormDialogComponent.value.dialogVisible = true
        addGeoFenceFormDialogComponent.value.state.key = key
      } else {
        //交车地查询交车地址
        const { data } = await getFleetOrderDropUpCheckApi({ dropUpPointId: e })
        state.formData.dropUpPointAddress = data.dropUpPointAddress
      }
    } else if (key === 'startFenceId' || key === 'endFenceId') {
      if (e === '-10000') {
        addGeoFenceFormDialogComponent.value.dialogVisible = true
        addGeoFenceFormDialogComponent.value.state.key = key
      }
    }
  }
  //远程模糊搜索下拉
  const remoteSelectMethod = async (query: string, key: string) => {
    if (query) {
      state.addressFuzzySearchParams.keyword = query
      const { data } = await getCommonOptionBusinessSelectApi(state.addressFuzzySearchParams)
      if (key === 'startFenceId') {
        startFenceIdOptions.value = data as SelectOptions[]
      } else if (key === 'endFenceId') {
        endFenceIdOptions.value = data as SelectOptions[]
      } else if (key === 'pickUpPointId') {
        pickUpPointIdOptions.value = data as SelectOptions[]
      } else if (key === 'dropUpPointId') {
        dropUpPointIdOptions.value = data as SelectOptions[]
      }
    }
  }
  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        //提交
        let params = JSON.parse(JSON.stringify(state.formData))
        if (state.formData.orderType === '合同订单') {
          params.paymentMethod = ''
          params.amountReceivable = 0
          params.collectUserName = ''
          params.whetherDriverCollect = 0
          params.haveDeliverySlip = 0
          params.planRefundDate = ''
        }

        console.log(params, 'params')

        if (params.id) {
          await putFleetOrderDetailApi(params)
          ElMessage.success('更新成功')
        } else {
          params = {
            amountReceivable: params.amountReceivable,
            brandName: params.brandName,
            businessType: params.businessType,
            childCompanyId: params.childCompanyId,
            collectUserName: params.collectUserName,
            customerId: params.customerId,
            customerOrderNo: params.customerOrderNo,
            dealerAddress: params.dealerAddress,
            dealerName: params.dealerName,
            downPaymentAmount: params.downPaymentAmount,
            downPaymentSeparateSettlement: params.downPaymentSeparateSettlement,
            dropUpPointAddress: params.dropUpPointAddress,
            dropUpPointId: params.dropUpPointId,
            endFenceId: params.endFenceId,
            expectedLoadTime: params.expectedLoadTime,
            haveDeliverySlip: params.haveDeliverySlip,
            modelName: params.modelName,
            orderIssueDatetime: params.orderIssueDatetime,
            orderNo: params.orderNo,
            orderType: params.orderType,
            paymentMethod: params.paymentMethod,
            pickUpPointAddress: params.pickUpPointAddress,
            pickUpPointId: params.pickUpPointId,
            planRefundDate: params.planRefundDate,
            predictDeliveryDatetime: params.predictDeliveryDatetime,
            remark: params.remark,
            startFenceId: params.startFenceId,
            twicePlan: params.twicePlan,
            vin: params.vin,
            whetherDriverCollect: 0,
          }
          await postFleetOrderApi(params)
          ElMessage.success('提交成功')
        }
        cancelForm(formEl)
        emit('refresh')
      } else {
      }
    })
  }
  const getDetail = async (id: string) => {
    const { data } = await getFleetOrderDetailApi({ id })
    state.formData = data as FleetOrderVO
    state.formData.twicePlan = data.twicePlan ? 1 : 0
    state.formData.whetherDriverCollect = data.whetherDriverCollect ? 1 : 0
    state.formData.haveDeliverySlip = data.haveDeliverySlip ? 1 : 0
    remoteSelectMethod(data.startArea, 'startFenceId')
    remoteSelectMethod(data.endArea, 'endFenceId')
    remoteSelectMethod(data.pickUpPointName, 'pickUpPointId')
    remoteSelectMethod(data.dropUpPointName, 'dropUpPointId')
  }
  const cancelForm = (formEl: FormInstance | undefined) => {
    formEl!.resetFields()
    state.formData.id = undefined
    state.dialogVisible.visible = false
    startFenceIdOptions.value = []
    state.formData.downPaymentSeparateSettlement = '否'
    state.formData.downPaymentAmount = 0
    endFenceIdOptions.value = []
    pickUpPointIdOptions.value = []
    dropUpPointIdOptions.value = []
    state.origin = 'add'
  }

  // 关闭子弹窗
  function closeMapDialog() {
    searchDialog.visible = false //关闭弹窗
  }
  // 子弹窗确认
  const submitLocation = (data: any) => {
    if (data) {
      state.geoFenceFormData.address = data.address
      state.geoFenceFormData.coordinate = data.coordinate
      closeMapDialog()
    }
  }
  interface GeoFenceFormData {
    name: string
    address: string
    area: string
    remark: string
    coordinate: string
    quyu: string
    radius: string
  }
  //获取围栏信息
  const geoFenceAddSuccess = async (data: GeoFenceFormData, key: string) => {
    console.log('%c [ key ] -> ', 'font-size:16px; background:#a9a346; color:#ede78a;', key)
    await remoteSelectMethod(data.name, key)
    if (key === 'pickUpPointId') {
      //默认选中第一项
      state.formData.pickUpPointId = pickUpPointIdOptions.value[0].value
      selectFence(state.formData.pickUpPointId, key)
    } else if (key === 'dropUpPointId') {
      //默认选中第一项
      state.formData.dropUpPointId = dropUpPointIdOptions.value[0].value
      selectFence(state.formData.dropUpPointId, key)
    } else if (key === 'startFenceId') {
      //默认选中第一项
      state.formData.startFenceId = startFenceIdOptions.value[0].value
    } else if (key === 'endFenceId') {
      //默认选中第一项
      state.formData.endFenceId = endFenceIdOptions.value[0].value
    }
  }
  const geoFenceAddClose = async (key: string) => {
    //重置围栏下拉
    if (key === 'pickUpPointId') {
      state.formData.pickUpPointId = ''
    } else if (key === 'dropUpPointId') {
      state.formData.dropUpPointId = ''
    } else if (key === 'startFenceId') {
      state.formData.startFenceId = ''
    } else if (key === 'endFenceId') {
      state.formData.endFenceId = ''
    }
  }
  const handleOrderTypeChange = (value: string) => {
    //清空字段
    state.formData.paymentMethod = ''
    state.formData.amountReceivable = 0
    state.formData.collectUserName = ''
    state.formData.whetherDriverCollect = 0
    state.formData.haveDeliverySlip = 0
    state.formData.planRefundDate = ''
  }
  defineExpose({
    state,
    getDetail,
  })
</script>
<style scoped lang="scss"></style>
