/*
 * @Author: llm
 * @Date: 2025-01-16 11:08:47
 * @LastEditors: llm
 * @LastEditTime: 2025-07-28 18:40:57
 * @Description:
 */
import request from '@/utils/request'

export function globalUploadV2Api(formData: any) {
  // const formData = new FormData();
  // formData.append("file", file);
  return request({
    url: `logistics/system/common/image/upload/v2`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * 上传文件
 * @param fileList 文件列表
 * @param data 数据
 * @param url 上传地址
 * @returns
 */
export function globalUploadApi(fileList: any[], data: any, url: string = 'logistics/system/common/image/upload/v3') {
  const formData = new FormData()
  fileList.forEach((item: any) => {
    formData.append('file', item.raw)
  })
  //遍历data  append 到formData
  Object.keys(data).forEach((key: string) => {
    formData.append(key, data[key])
  })
  return request({
    url,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
export function globalUploadV3Api(fileList: any[], data: any) {
  const formData = new FormData()
  fileList.forEach((item: any) => {
    formData.append('file', item.raw)
  })
  //遍历data  append 到formData
  Object.keys(data).forEach((key: string) => {
    formData.append(key, data[key])
  })
  return request({
    url: `logistics/system/common/image/upload/v3`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
export function globalUploadVideoV2Api(formData: any) {
  // const formData = new FormData();
  // formData.append("file", file);
  return request({
    url: `/logistics/system/common/video/upload/v2`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
/**
 * 获取打印模板
 * @param params{
 * menuId: 菜单id
 * }
 * @returns
 */
export function getPrintTemplateConfigApi(id: string, uri: string) {
  return request({
    url: `${uri}/print/${id}`,
    method: 'get',
  })
}
/**
 * @description: 公共图片上传
 * @param {string} imageBase64
 * @return {*}
 */

export function globalUploadImage(data: any) {
  return request({
    url: '/operation/v1/api/image/user/upload',
    method: 'post',
    data,
  })
}
/**
 * 地点搜索下拉提示
 * @param {*} data
 * @returns
 */
export function bdMapSuggestion(data: any) {
  return request({
    url: 'api/hdd/backend/utils/bd/suggestion',
    method: 'post',
    data,
  })
}
