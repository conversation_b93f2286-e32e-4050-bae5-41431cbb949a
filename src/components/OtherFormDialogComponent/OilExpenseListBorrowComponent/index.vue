<!--
 * @Author: llm
 * @Date: 2025-05-24 09:50:34
 * @LastEditors: llm
 * @LastEditTime: 2025-06-04 11:36:42
 * @Description: 油费借支弹窗
-->
<script lang="ts" setup>
  import { getFleetOrderDispatchOilFeePreviewApi } from '@/api/businessManagement'
  import {
    getCommonOptionBusinessSelectApi,
    getOutFleetLoanOilFeeDetailApi,
    postOutFleetLoanOilFeeAddApi,
    postOutFleetLoanOilFeeEditApi,
  } from '@/api/financialManagement'
  import { getNowDateYMD } from '@/utils'
  import { FormInstance } from 'element-plus'
  import { debounce } from 'lodash'
  const oilBorrowFormRef = ref<FormInstance>()
  const emit = defineEmits(['refresh'])
  const state = reactive({
    dispatchCreateDate: '',
    oilBorrowDialogVisible: false,
    rules: {
      dispatchNo: [{ required: true, message: '请选择调度单号', trigger: 'change' }],
      applyDate: [{ required: true, message: '请选择借款日期', trigger: 'change' }],
      baseOilPrice: [{ required: true, message: '请输入基地油单价', trigger: 'blur' }],
    },
    oilBorrowFormData: {} as any,
    oilBorrowPreviewInfo: {} as any,
    currentDispatchNo: '',
    status: '',
    baseFuelCost: 0,
    oilBorrowTableData: [] as any[],
    dispatchList: [] as any[],
    loading: false,
  })
  watch(
    () => state.oilBorrowDialogVisible,
    () => {
      //applyDate 默认当天
      if (state.oilBorrowDialogVisible && state.status === 'add') {
        state.oilBorrowFormData.applyDate = getNowDateYMD()
      }
    },
  )
  watch([() => state.oilBorrowFormData.baseOilLiters, () => state.oilBorrowFormData.baseOilPrice], () => {
    state.baseFuelCost = Number((state.oilBorrowFormData.baseOilLiters * state.oilBorrowFormData.baseOilPrice || 0).toFixed(2))
  })
  watch([() => state.oilBorrowFormData.otherOilLiters, () => state.oilBorrowFormData.otherOilPrice], () => {
    state.oilBorrowFormData.otherFuelCost = Number((state.oilBorrowFormData.otherOilLiters * state.oilBorrowFormData.otherOilPrice || 0).toFixed(2))
  })
  const closeOilBorrowDialog = () => {
    state.oilBorrowDialogVisible = false
    state.oilBorrowFormData = {}
    state.oilBorrowPreviewInfo = {}
    state.dispatchList = []
    state.oilBorrowTableData = []
    state.baseFuelCost = 0
    state.status = 'add'
    state.currentDispatchNo = ''
    // 清空表单验证
    oilBorrowFormRef.value?.clearValidate()
  }
  const confirmOilBorrowDialog = debounce((formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate(async (valid: any) => {
      if (valid) {
        state.oilBorrowFormData.baseFuelCost = Number(state.baseFuelCost.toFixed(2))
        state.oilBorrowFormData.oilItems = state.oilBorrowPreviewInfo.oilItems
        if (state.oilBorrowFormData.id) {
          await postOutFleetLoanOilFeeEditApi(state.oilBorrowFormData)
        } else {
          await postOutFleetLoanOilFeeAddApi(state.oilBorrowFormData)
        }
        ElMessage.success('操作成功')
        state.oilBorrowDialogVisible = false

        emit('refresh')
      }
    })
  }, 300)
  const getDetail = (id: string) => {
    state.loading = true
    getOutFleetLoanOilFeeDetailApi({ id })
      .then((res: any) => {
        const { data } = res
        state.oilBorrowFormData.baseFuelCost = Number(data.baseFuelCost.toFixed(2))
        state.oilBorrowFormData.baseOilLiters = data.baseOilLiters
        state.oilBorrowFormData.baseOilPrice = data.baseOilPrice
        state.oilBorrowFormData.dispatchNo = data.dispatchNo
        state.oilBorrowFormData.estimatedOilCapacity = data.estimatedOilCapacity
        state.oilBorrowFormData.otherFuelCost = Number(data.otherFuelCost.toFixed(2))
        state.oilBorrowFormData.otherOilLiters = data.otherOilLiters
        state.oilBorrowFormData.otherOilPrice = data.otherOilPrice
        state.oilBorrowFormData.applyDate = data.applyDate
        state.oilBorrowFormData.id = data.id
        state.oilBorrowFormData.remark = data.remarks
        getDispatchInfo(data.dispatchNo, data)
      })
      .finally(() => {
        state.loading = false
      })
  }
  const getDispatchList = async (query: string) => {
    if (!query) return
    const params = {
      label: 'name',
      dataSource: '未结算调度单',
      value: 'code',
      fuzzy: true,
      keyword: query,
      selfFleet: true,
      extra: true,
    }
    const { data } = await getCommonOptionBusinessSelectApi(params)
    state.dispatchList = data as any[]
  }
  const getDispatchInfo = async (e: string, _data: any) => {
    if (!e) return
    console.log(e)
    getFleetOrderDispatchOilFeePreviewApi({ dispatchNo: e }).then((res: any) => {
      const { data } = res
      state.oilBorrowPreviewInfo = data
      state.dispatchCreateDate = data.dispatchCreateDate
      state.oilBorrowFormData.baseOilPrice = data.baseOilPrice || 0
      state.oilBorrowFormData.otherOilPrice = data.otherOilPrice || 0
      if (_data) {
        data.oilItems[0].rechargeAmount = _data.sinopecAmount || 0
        data.oilItems[1].rechargeAmount = _data.petroChinaAmount || 0
        data.oilItems[2].rechargeAmount = _data.wanjinOilAmount || 0
      }

      state.oilBorrowPreviewInfo.oilItems = data.oilItems
      // state.oilBorrowFormData.baseOilLiters = data.baseOilLiters
      // state.oilBorrowFormData.baseOilPrice = _data.baseOilPrice
      // state.oilBorrowFormData.otherOilLiters = _data.otherOilLiters
      // state.oilBorrowFormData.otherOilPrice = _data.otherOilPrice
      // state.oilBorrowFormData.remark = _data.remark
      // state.oilBorrowFormData.oilItems = data.oilItems
      // state.oilBorrowFormData.estimatedOilCapacity = data.estimatedOilCapacity
    })
  }
  defineExpose({
    getDetail,
    state,
  })
</script>
<template>
  <el-dialog title="油费借支" v-model="state.oilBorrowDialogVisible" width="60%" @close="closeOilBorrowDialog">
    <el-scrollbar max-height="65vh" v-loading="state.loading">
      <el-form :rules="state.rules" class="mt-20px" ref="oilBorrowFormRef" :model="state.oilBorrowFormData" label-width="120px">
        <el-row>
          <el-col>
            <el-form-item label="调度单号" prop="dispatchNo">
              <!-- <el-input placeholder="请填写" :maxLength="20" @blur="getDispatchList" v-model="state.formData.vin"></el-input> -->
              <el-select
                v-model="state.oilBorrowFormData.dispatchNo"
                filterable
                remote
                reserve-keyword
                remote-show-suffix
                :remote-method="getDispatchList"
                :disabled="state.status === 'edit'"
                placeholder="请选择"
                clearable
                @change="getDispatchInfo"
              >
                <el-option v-for="item in state.dispatchList" :key="item.id" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-descriptions title="" border :column="2" class="mb-20px">
          <el-descriptions-item :width="140" label="车牌号">{{ state.oilBorrowPreviewInfo.vehicleNo || '-' }}</el-descriptions-item>
          <el-descriptions-item :width="140" label="司机信息"
            >{{ state.oilBorrowPreviewInfo.driverName || '-' }}（{{ state.oilBorrowPreviewInfo.driverMobile || '-' }}）</el-descriptions-item
          >
          <el-descriptions-item :width="140" label="运输动态">
            <span v-html="state.oilBorrowPreviewInfo.showDispatchLine"></span>
          </el-descriptions-item>
          <el-descriptions-item :width="140" label="里程">{{ state.oilBorrowPreviewInfo.mileage || '-' }}</el-descriptions-item>
          <!-- <el-descriptions-item :width="140" label="标准油箱升数">{{ state.oilBorrowPreviewInfo.standardOilTankCapacity || '-' }}L</el-descriptions-item>
          <el-descriptions-item :width="140" label="预留油升数">{{ state.oilBorrowPreviewInfo.reserveOilCapacity || '-' }}L</el-descriptions-item> -->
          <el-descriptions-item :width="140" label="预估油升数">{{ state.oilBorrowPreviewInfo.estimatedOilCapacity || '-' }}</el-descriptions-item>
          <el-descriptions-item :width="140" label="已借金额">{{ state.oilBorrowPreviewInfo.totalPrice || '-' }}</el-descriptions-item>
        </el-descriptions>
        <el-row :gutter="0">
          <el-col :span="8">
            <el-form-item label="借款日期" prop="applyDate">
              <el-date-picker
                v-model="state.oilBorrowFormData.applyDate"
                type="date"
                placeholder="请选择借款日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabledDate="
                  (time: any) => time > new Date() || time <= new Date(state.dispatchCreateDate).setDate(new Date(state.dispatchCreateDate).getDate() - 1)
                "
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="0">
          <el-col :span="8">
            <el-form-item label="基地油升数" prop="baseOilLiters">
              <el-input-number
                :style="{ width: '100%' }"
                v-model="state.oilBorrowFormData.baseOilLiters"
                placeholder="请输入基地油升数"
                :min="0"
                :precision="2"
              >
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="基地油单价" prop="baseOilPrice" :rules="[{ required: true, message: '请输入基地油单价', trigger: 'blur' }]">
              <el-input-number :style="{ width: '100%' }" v-model="state.oilBorrowFormData.baseOilPrice" placeholder="请输入基地油单价" :min="0" :precision="2">
                <template #suffix>
                  <span>元/L</span>
                </template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="基地油费" prop="baseFuelCost">
              <el-input-number :style="{ width: '100%' }" v-model="state.baseFuelCost" placeholder="请输入基地油费" :min="0" :precision="2">
                <template #suffix>
                  <div class="flex items-center">
                    <span>元</span>
                    <el-tooltip effect="dark" content="基地油费=基地油升数*基地油单价" placement="bottom">
                      <el-icon :size="16" class="ml-5px" color="orange">
                        <Warning />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="其他油升数" prop="otherOilLiters">
              <el-input-number
                :style="{ width: '100%' }"
                v-model="state.oilBorrowFormData.otherOilLiters"
                placeholder="请输入其他油升数"
                :min="0"
                :precision="1"
              >
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="其他油单价" prop="otherOilPrice" :rules="[{ required: true, message: '请输入其他油单价', trigger: 'blur' }]">
              <el-input-number
                :style="{ width: '100%' }"
                v-model="state.oilBorrowFormData.otherOilPrice"
                placeholder="请输入其他油单价"
                :min="0"
                :precision="2"
              >
                <template #suffix>
                  <span>元/L</span>
                </template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="其他油总价" prop="otherFuelCost">
              <el-input-number
                :disabled="true"
                :style="{ width: '100%' }"
                v-model="state.oilBorrowFormData.otherFuelCost"
                placeholder="请输入其他油总价"
                :min="0"
                :precision="2"
              >
                <template #suffix>
                  <span>元</span>
                </template>
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <span>油卡信息</span>
            </div>
          </template>
          <el-table :data="state.oilBorrowPreviewInfo.oilItems" :border="true">
            <el-table-column label="类型" prop="oilCardTypeName" align="center"></el-table-column>
            <el-table-column label="卡号" prop="oilCardNumber" align="center"></el-table-column>
            <el-table-column label="余额" prop="cardBalance" align="center">
              <template #default="{ row }">
                <el-text type="danger">{{ row.cardBalance || 0 }}</el-text>
              </template>
            </el-table-column>
            <el-table-column label="预估剩余油升数" prop="oilCardAmount" align="center">
              <template #default="{ row }">
                <el-text type="danger" v-if="state.oilBorrowFormData.otherOilPrice">{{
                  (row.cardBalance / state.oilBorrowFormData.otherOilPrice).toFixed(2)
                }}</el-text>
                <el-text type="danger" v-else>--</el-text>
              </template>
            </el-table-column>
            <el-table-column label="充值金额" prop="rechargeAmount" align="center">
              <template #default="{ row }">
                <el-input-number :style="{ width: '100%' }" v-model="row.rechargeAmount" placeholder="请输入充值金额" :min="0" :precision="2">
                  <template #suffix>
                    <span>元</span>
                  </template>
                </el-input-number>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
        <el-row :gutter="0" class="mt-20px">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="state.oilBorrowFormData.remark" type="textarea" placeholder="请输入备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeOilBorrowDialog">取消</el-button>
        <el-button type="primary" @click="confirmOilBorrowDialog(oilBorrowFormRef)">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
