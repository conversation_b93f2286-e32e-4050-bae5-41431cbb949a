<!--
 * @Author: llm
 * @Date: 2023-07-05 21:41:09
 * @LastEditors: llm
 * @LastEditTime: 2025-05-19 10:30:08
 * @Description: 顶部查询按钮组
 *
-->
<template>
  <!-- <el-card class="top-query" shadow="never" :style="{ position: 'fixed', height: moreSearch ? 'auto' : '36px', zIndex: 99, marginBottom: '20px' }"> -->
  <div class="query-container" :style="{ height: moreSearch ? 'auto' : '48px' }">
    <div class="query-box">
      <div class="query-container-left">
        <el-form size="small" ref="queryFormRef" :model="queryParams" :inline="true" style="width: 100%">
          <el-row :gutter="20">
            <el-col :span="8" v-for="(item, index) in filterDataColumn" :key="index">
              <div v-if="item?.selectEnable" style="display: inline-block; width: 100%" v-show="moreSearch || index < 3">
                <el-form-item
                  :label="item.query?.label"
                  :prop="item.query?.name"
                  :rules="item.query?.rules"
                  :label-width="item.query?.labelWidth ? item.query?.labelWidth : '70px'"
                  style="width: 100%"
                  v-if="
                    !item.query?.dependsOn ||
                    item.query.dependsOn.every((depend: DependsOn) => {
                      if (!depend.operator) {
                        depend.operator = 'eq'
                      }
                      const target = queryParams[depend.field!]
                      const when = depend.when!
                      return operatorCalculate(target, when, depend.operator)
                    })
                  "
                >
                  <div style="width: 100%" class="flex items-center">
                    <!-- textarea -->
                    <el-input
                      style="width: 100%"
                      v-model="queryParams[item.query.name as keyof TableItem] as any"
                      type="textarea"
                      :rows="1"
                      clearable
                      :placeholder="item.query.placeholder ? item.query.placeholder : '请输入' + item.query.label"
                      v-if="item.query?.type === 'textarea'"
                    />
                    <!-- textBatch -->
                    <div v-if="item.query?.type === 'textBatch'" style="position: relative">
                      <el-input
                        style="width: 100%"
                        v-model="queryParams[item.query.name as keyof TableItem] as any"
                        type="textBatch"
                        :rows="1"
                        clearable
                        :placeholder="item.query.placeholder ? item.query.placeholder : '请输入' + item.query.label"
                        @click="openTextBatch(item.query.name)"
                        @clear="emptyTextBatch(item.query.name)"
                      />
                      <div
                        v-show="showTextBatch && item.query.name == currentBatchField"
                        style="width: 100%; position: absolute; top: 0px; left: 0px; z-index: 9999"
                      >
                        <VINbatch
                          ref="vinBatchRef"
                          @arraySent="handleArrayReceived"
                          :showTextBatch="showTextBatch"
                          :targetField="{ ...item.query, message: `${item.query.message}` }"
                          :closeTextBatch="closeTextBatch"
                          :initialValue="currentBatchValue"
                        />
                      </div>
                    </div>
                    <!-- 下拉选择 -->
                    <el-select
                      v-model="queryParams[item.query.name as keyof TableItem] as any"
                      style="width: 100%"
                      :value-key="item.query.name"
                      :multiple="item.query?.option?.multiple"
                      :collapse-tags="item.query?.option?.multiple"
                      :collapse-tags-tooltip="item.query?.option?.multiple"
                      :placeholder="item.query.placeholder ? item.query.placeholder : '请选择' + item.query.label"
                      filterable
                      clearable
                      @clear="() => clearDropdownData(item.query!.name!)"
                      v-else-if="item.query?.type === 'select'"
                    >
                      <el-option v-for="i in item.query?.option?.data" :key="i.value" :label="i.label" :value="i.value" />
                    </el-select>
                    <!-- 下拉树 -->
                    <el-tree-select
                      v-model="queryParams[item.query.name!]"
                      default-expand-all
                      filterable
                      clearable
                      style="width: 100%"
                      :multiple="item.query?.option?.multiple"
                      :collapse-tags="item.query?.option?.multiple"
                      :collapse-tags-tooltip="item.query?.option?.multiple"
                      :check-strictly="item.query?.option?.checkStrictly || false"
                      :placeholder="item.query.placeholder ? item.query.placeholder : '请选择' + item.query.label"
                      :data="item.query.option?.data"
                      :props="{ value: 'value', label: 'label' }"
                      :disabled="item.canEdit"
                      @clear="() => clearDropdownData(item.query!.name!)"
                      v-else-if="item.query?.type === 'selectTree'"
                    >
                      <template #default="{ data }">{{ data.label }}</template>
                    </el-tree-select>
                    <div v-else-if="item.query?.type === 'fuzzySelect'" style="width: 100%">
                      <!-- 远程模糊搜索下拉 -->
                      <el-select
                        v-model="queryParams[item.query.name as keyof TableItem] as any"
                        style="width: 100%"
                        remote-show-suffix
                        clearable
                        :value-key="item.query.name"
                        :multiple="item.query?.option?.multiple"
                        :collapse-tags="item.query?.option?.multiple"
                        :collapse-tags-tooltip="item.query?.option?.multiple"
                        filterable
                        remote
                        reserve-keyword
                        :placeholder="item.query.placeholder ? item.query.placeholder : '模糊搜索' + item.query.label"
                        :remote-method="(query: string) => remoteSelectMethod(query, item.query?.name!)"
                        :disabled="item.canEdit"
                        :loading="fuzzySelectLoading"
                        @clear="() => clearDropdownData(item.query!.name!)"
                      >
                        <el-option v-for="i in item.query?.option?.data || []" :key="i.value" :label="i.label" :value="i.value" />
                      </el-select>
                    </div>
                    <!-- 年月日 -->
                    <!-- <el-date-picker
                          v-model="(queryParams[item.query.name as keyof TableItem] as any)"
                          type="date"
                          :placeholder="
                            item.query.placeholder
                              ? item.query.placeholder
                              : '请选择' + item.query.label
                          "
                          :format="item.query.format"
                          :value-format="item.query.format"
                          v-else-if="item.query?.type === 'date'"
                        /> -->
                    <!-- 日区间、月区间-->
                    <el-date-picker
                      style="width: 100%"
                      v-model="queryParams[item.query.name as keyof TableItem] as any"
                      :type="item.query?.type"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      clearable
                      :placeholder="item.query.placeholder ? item.query.placeholder : '请选择' + item.query.label"
                      :format="item.query.format"
                      :value-format="item.query.format"
                      :shortcuts="item.query?.type === 'daterange' || item.query?.type === 'datetimerange' ? ShortcutsList : []"
                      @change="(value: any) => handleDateRangeChange(item, value)"
                      @clear="() => clearDropdownData(item.query!.name!)"
                      v-else-if="item.query?.type === 'daterange' || item.query?.type === 'monthrange' || item.query?.type === 'datetimerange'"
                    />
                    <!-- 周、年、月、日 -->
                    <el-date-picker
                      style="width: 100%"
                      v-model="queryParams[item.query.name as keyof TableItem] as any"
                      :type="item.query?.type"
                      clearable
                      :placeholder="item.query.placeholder ? item.query.placeholder : '请选择' + item.query.label"
                      :format="item.query.format"
                      :value-format="item.query.format"
                      :picker-options="{ firstDayOfWeek: 1 }"
                      @clear="() => clearDropdownData(item.query!.name!)"
                      v-else-if="item.query?.type === 'week' || item.query?.type === 'year' || item.query?.type === 'month' || item.query?.type === 'date'"
                    />

                    <!-- 级联选择器-省市区 -->
                    <el-cascader
                      style="width: 100%"
                      v-model="queryParams[item.query.name as keyof TableItem] as any"
                      clearable
                      filterable
                      collapse-tags
                      collapse-tags-tooltip
                      :options="item.query!.option?.data"
                      :props="{ multiple: item.query!.option?.multiple, checkStrictly: item.query!.option?.checkStrictly }"
                      @clear="() => clearDropdownData(item.query!.name!)"
                      v-else-if="item.query?.type === 'cascader'"
                    />
                    <!-- 默认都是input -->
                    <el-input
                      v-model="queryParams[item.query?.name as keyof TableItem] as any"
                      :placeholder="item.query!.placeholder ? item.query!.placeholder : '请输入' + item.query!.label"
                      style="width: 100%"
                      clearable
                      v-else
                    />
                    <div v-if="item.query?.longTerm" class="ml-5px mr-5px">
                      <el-checkbox @change="(e: any) => handleLongTerm(item, e)" v-model="queryParams[(item.query?.name + 'LongTerm') as keyof TableItem]"
                        >长期</el-checkbox
                      >
                    </div>
                  </div>
                </el-form-item>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="query-container-right" :style="{ width: showMoreBtn ? '240px' : '180px' }" v-if="showSearch">
        <div style="display: flex; justify-content: start; align-items: center; padding-top: 1px">
          <!-- icon="More" -->
          <el-button size="small" round @click="moreClick" v-if="showMoreBtn">更多</el-button>
          <el-button size="small" class="filter-item" type="primary" @click="handleQuery">
            <i-ep-search />
            搜索
          </el-button>
          <el-button @click="resetQuery" size="small">
            <i-ep-refresh />
            重置
          </el-button>
          <el-icon style="margin-left: 4px" size="16" @click="showCustomQuerySort" v-if="showCustomTableHeaderBtn">
            <Tools />
          </el-icon>
        </div>
      </div>
    </div>
    <!-- 自定义排序弹窗 -->
    <CustomQuerySortComponent ref="customQuerySortRef" :requestUri="requestUri" @refreshPageTableColumn="refreshPageTableColumn" />
  </div>

  <!-- </el-card> -->
</template>
<script setup lang="ts">
  // 状态管理依赖
  import { useFormStore } from '@/store/modules/form.js'
  import { ShortcutsList } from '@/utils/index'
  const formStore = useFormStore()
  import { cloneDeep } from 'lodash'
  import { dayjs, ElForm } from 'element-plus'
  import { getQuerySelectOptions, getSelectOptions, operatorCalculate } from '@/utils/common'
  import VINbatch from './components/VINbatch.vue'
  import CustomQuerySortComponent from '@/components/CustomQuerySortComponent/index.vue'
  const showTextBatch = ref(false)
  const vinBatchRef = ref()
  const currentBatchField = ref('')
  const currentBatchValue = ref('')
  const showCustomTableHeaderBtn = ref(false)
  function openTextBatch(name: any) {
    currentBatchField.value = name
    currentBatchValue.value = queryParams[name] || '' // 保存当前值
    showTextBatch.value = true
  }

  const handleArrayReceived = (array: any, targetField: { name: string | number }) => {
    queryParams[targetField.name] = Object.values(array).join(',')
    currentBatchValue.value = queryParams[targetField.name]
  }

  const emptyTextBatch = (name: any) => {
    const textBatchItems = filterDataColumn.value.filter((item) => item.query?.type === 'textBatch')
    const index = textBatchItems.findIndex((item) => item.query?.name === name)
    if (index !== -1 && vinBatchRef.value?.[index]?.list) {
      vinBatchRef.value[index].list = []
    }
  }

  // 清空下拉框数据
  const clearDropdownData = (fieldName: string) => {
    if (fieldName) {
      // 找到对应的查询项配置
      const queryItem = filterDataColumn.value.find((item) => item.query?.name === fieldName)

      if (queryItem) {
        const queryType = queryItem.query?.type

        // 根据不同类型清空数据
        if (queryType === 'daterange' || queryType === 'monthrange' || queryType === 'datetimerange') {
          // 日期范围类型设置为空数组
          queryParams[fieldName] = []
        } else if (queryItem.query?.option?.multiple) {
          // 多选情况下设置为空数组
          queryParams[fieldName] = []
        } else {
          // 单选情况下设置为null
          queryParams[fieldName] = null
        }

        // 如果是远程搜索下拉，清空选项数据
        if (queryType === 'fuzzySelect' && queryItem.query?.option?.data) {
          queryItem.query.option.data = []
        }

        // 如果有长期选项，也清空长期选择状态
        if (queryItem.query?.longTerm) {
          queryParams[fieldName + 'LongTerm'] = false
        }
      }
    }
  }
  const customQuerySortRef = ref()
  //自定义表头
  const showCustomQuerySort = () => {
    customQuerySortRef.value.state.visible = true
    customQuerySortRef.value.getCustomTableSearch(props.requestUri)
  }
  const refreshPageTableColumn = () => {
    emit('refreshPageTableColumn')
  }
  const closeTextBatch = () => {
    showTextBatch.value = false
  }

  const { proxy }: any = getCurrentInstance()
  //@ts-ignore
  dayjs.en.weekStart = 1
  const emit = defineEmits(['handleSearchQuery', 'queryParams', 'exportExcel', 'refreshPage', 'refreshPageTableColumn'])
  let queryParams = reactive<any>({ page: 1 })
  const filterDataColumn = ref<TableItem[]>([])
  /**
   * 用于提交用的临时params
   */
  let tempQueryParams = reactive<any>({})
  const queryFormRef = ref(ElForm)
  const exportButtonLoading = ref(false)
  const moreSearch = ref(false)
  const showMoreBtn = ref(false)
  const props = defineProps({
    /**
     * 查询条件组
     */
    queryPermissionGroup: {
      require: false,
      type: Array<TableItem>,
      default: [],
    },
    showSearch: {
      require: false,
      type: Boolean,
      default: true,
    },
    /**
     * 顶部按钮权限
     */
    topButtonPermissionGroup: {
      require: false,
      type: Array as PropType<MenuVO[]>,
      default: () => {
        return []
      },
    },
    requestUri: {
      require: false,
      type: String,
      default: '',
    },
  })
  watch(
    () => props.topButtonPermissionGroup,
    () => {
      //props.topButtonPermissionGroup 存在item.meta?.purpose === 'definePageHeader'项则showCustomTableHeaderBtn=true
      showCustomTableHeaderBtn.value = props.topButtonPermissionGroup.some((item) => item.meta?.purpose === 'definePageHeader')
    },
    {
      immediate: true,
      deep: true,
    },
  )
  /**
   * 对dataColumn进行排序展示
   */
  watch(
    () => props.queryPermissionGroup,
    (dataColumn: TableItem[]) => {
      filterDataColumn.value = dataColumn.filter((item) => item.query && item.selectEnable)
      if (filterDataColumn.value.length > 3) {
        showMoreBtn.value = true
      } else {
        showMoreBtn.value = false
      }
      filterDataColumn.value.sort((item1, item2) => item1.query?.sortNo! - item2.query?.sortNo!)
      //以下代码 => 当下拉项只有一条的时候默认选中
      filterDataColumn.value.forEach((item) => {
        if (item.query?.type === 'select' && item.query?.option?.data && item.query?.option?.data!.length === 1) {
          if (item.query.option.multiple) {
            queryParams[item.query.name!] = [item.query.option.data[0].value]
          } else {
            queryParams[item.query.name!] = item.query.option.data[0].value
          }
        }
        // Handle dynamic values
        if (item.query?.dynamicValue !== undefined) {
          const dynamicValue = item.query.dynamicValue
          const type = item.query.type
          const format = item.query.format || 'YYYY-MM-DD'

          if (type === 'month') {
            queryParams[item.query.name!] = dayjs().add(dynamicValue, 'month').format(format)
          } else if (type === 'year') {
            queryParams[item.query.name!] = dayjs().add(dynamicValue, 'year').format(format)
          } else if (type === 'date') {
            queryParams[item.query.name!] = dayjs().add(dynamicValue, 'day').format(format)
          } else if (type === 'daterange') {
            const start = dayjs().add(dynamicValue, 'day')
            const end = dayjs()
            queryParams[item.query.name!] = [start.format(format), end.format(format)]
          }
        }
      })
    },
    { deep: true, immediate: true },
  )
  let oldState = cloneDeep(queryParams)
  watch(
    () => queryParams,
    (newState) => {
      let localNewState = toRaw(newState)
      for (const key in newState) {
        // 遍历 dataColumn 数组
        let dependOn = [] as any
        filterDataColumn.value.forEach((item: TableItem) => {
          //如果item.query.name === key,并且item.query.option.refreshPage===true,则刷新列表数据
          if (item.query && item.query?.name === key && item.query?.option?.refreshPage) {
            handleQuery()
          }
          if (item.query && item.query?.option?.dependOn === key && item.selectEnable) {
            //判断toRaw(newState)[key] 和 oldState[key] 这两个数组是否相同，
            //如果相同说明没有变化，直接返回
            let dependOnValue = ''
            let oldStayKeyValue = ''
            if (Array.isArray(localNewState[key])) {
              dependOnValue = localNewState[key].join(',')
            } else {
              dependOnValue = localNewState[key]
            }
            if (Array.isArray(oldState[key])) {
              oldStayKeyValue = oldState[key]!.join(',')
            } else {
              oldStayKeyValue = oldState[key]
            }
            //防止其他表单项变化 触发多次调
            if (dependOnValue === oldStayKeyValue) {
              return
            }
            //新旧值不同 清空关联表单
            if (oldStayKeyValue && oldStayKeyValue != dependOnValue) {
              if (item.form?.type === 'number') {
                queryParams[item.query?.name!] = 0
              } else if ((item.query?.type === 'timerange' || item.query?.type === 'daterange') && Array.isArray(queryParams[item.query?.name!])) {
                queryParams[item.query?.name!] = []
              } else {
                queryParams[item.query?.name!] = null
              }
            }
            //防止多次调
            if (dependOn[dependOnValue]) {
              return
            }
            dependOn[dependOnValue] = queryParams[key]
            emit('queryParams', queryParams, item.query?.option?.dependOn)
          }
        })
      }

      oldState = cloneDeep(newState)
    },
    { deep: true },
  )

  /**
   * 查询
   */

  const handleQuery = async () => {
    await searchQueryTemp()
    moreSearch.value = false
    emit('handleSearchQuery', tempQueryParams, 'request')
  }
  interface ShortcutsVO {
    text: string
    startOffset: number
    endOffset: number
  }

  // 更多搜索项
  const moreClick = () => {
    moreSearch.value = !moreSearch.value
  }

  function parseShortcuts(shortcuts: Array<ShortcutsVO>) {
    return shortcuts.map((shortcut: ShortcutsVO) => ({
      text: shortcut.text,
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() + 3600 * 1000 * 24 * shortcut.startOffset)
        end.setTime(end.getTime() + 3600 * 1000 * 24 * shortcut.endOffset)
        return [start, end]
      },
    }))
  }
  const fuzzySelectLoading = ref(false)
  //远程模糊搜索下拉
  const remoteSelectMethod = async (query: string, key: string) => {
    if (query) {
      const item = filterDataColumn.value.find((item) => item.query?.name === key)
      if (item) {
        fuzzySelectLoading.value = true
        const dataColumn = await getQuerySelectOptions(filterDataColumn.value, null, '', 'formSelect', key, { keyword: query })
        fuzzySelectLoading.value = false
      }
    }
  }
  /**
   * 转换传参
   */

  const searchQueryTemp = () => {
    return new Promise((resolve, reject) => {
      tempQueryParams = JSON.parse(JSON.stringify(queryParams))
      //如果tempQueryParams中key包含longTerm，则定义新的key，将值赋值为9999-12-31
      Object.keys(tempQueryParams).forEach((key) => {
        if (key.includes('LongTerm')) {
          const originalKey = key.replace('LongTerm', '')
          if (tempQueryParams[key]) {
            // Find the corresponding query configuration
            const queryConfig = filterDataColumn.value.find((item) => item.query?.name === originalKey)
            if (queryConfig?.query?.type === 'daterange' && queryConfig?.query?.format) {
              // Use the format from query configuration
              tempQueryParams[originalKey] = ['9999-12-31', '9999-12-31']
            }
          }
          delete tempQueryParams[key]
        }
      })

      //遍历tempQueryParams，找出每项类型是数组的，转成字符串
      Object.keys(tempQueryParams).forEach((key) => {
        if (tempQueryParams[key] && tempQueryParams[key].constructor === Array) {
          //找filterDataColumn.value中的某项的query.type="cascader",并且query.option.multiple=true
          filterDataColumn.value.map((item) => {
            if (tempQueryParams[item.query?.name!] && tempQueryParams[item.query?.name!].constructor === Array) {
              if (item.query?.name === key && item.query.option?.multiple) {
                let arr = tempQueryParams[key]
                if (item.query?.type === 'cascader') {
                  //将tempQueryParams[item.query.name!]二维数组转成字符串并赋值给 tempQueryParams[item.query.name!]
                  arr = tempQueryParams[key].map((_item: any) => _item.toString()).join(';')
                } else if (item.query?.type === 'select' || item.query?.type === 'fuzzySelect') {
                  arr = tempQueryParams[key].join(',')
                }
                tempQueryParams[key] = arr
              }
            }
          })
        }
      })

      let searchQueryTemp = JSON.parse(JSON.stringify(tempQueryParams)) //去除时间数组不需要的参数

      for (const key in props.queryPermissionGroup) {
        if (Object.prototype.hasOwnProperty.call(props.queryPermissionGroup, key)) {
          if (tempQueryParams[props.queryPermissionGroup[key].query?.name!]) {
            const name = props.queryPermissionGroup[key].query?.name!
            //如果属性中包含range字符串，则把首字母大写，然后拆分2个，分别拼上start 和 end ,并删除原有属性
            if (props.queryPermissionGroup[key].query && props.queryPermissionGroup[key].query?.type.indexOf('range') !== -1) {
              const startName = 'start' + name.charAt(0).toUpperCase() + name.slice(1)
              const endName = 'end' + name.charAt(0).toUpperCase() + name.slice(1)
              searchQueryTemp[startName] = tempQueryParams[startName] = tempQueryParams[name][0]
              searchQueryTemp[endName] = tempQueryParams[endName] = tempQueryParams[name][1]
              delete searchQueryTemp[name]
            }
          }
        }
      }
      tempQueryParams = { ...searchQueryTemp }
      formStore.setSearchParams(tempQueryParams)
      resolve(tempQueryParams)
    })
  }

  /**
   * 重置查询 不调接口
   */
  function resetParams() {
    queryFormRef.value.resetFields()
    //删除所有属性
    for (const key in tempQueryParams) {
      if (key !== 'page' && key !== 'limit') {
        tempQueryParams[key] = undefined
      }
    }
    for (const key in queryParams) {
      if (key !== 'page' && key !== 'limit') {
        queryParams[key] = undefined
      }
    }
    //获取全局中的参数并赋值给tempQueryParams
    const params = proxy.$sideBarStore.$state.btnMenuQuery
    for (const key in params) {
      tempQueryParams[key] = params[key]
    }
    emit('handleSearchQuery', tempQueryParams, 'noRequest')
  }

  /**
   * 重置查询 调接口
   */
  function resetQuery() {
    queryFormRef.value.resetFields()
    //删除所有属性
    for (const key in tempQueryParams) {
      if (key !== 'page' && key !== 'limit') {
        tempQueryParams[key] = undefined
      }
    }
    for (const key in queryParams) {
      if (key !== 'page' && key !== 'limit') {
        queryParams[key] = undefined
      }
    }
    //如果存在默认值，则赋值默认值
    filterDataColumn.value.map((item) => {
      if (item.query?.defaultValue) {
        tempQueryParams[item.query?.name!] = item.query?.defaultValue
      }
      // Reset longTerm checkboxes to false
      if (item.query?.longTerm) {
        queryParams[item.query?.name! + 'LongTerm'] = false
        tempQueryParams[item.query?.name! + 'LongTerm'] = false
      }
    })

    //获取全局中的参数并赋值给tempQueryParams
    const params = proxy.$sideBarStore.$state.btnMenuQuery
    for (const key in params) {
      tempQueryParams[key] = params[key]
    }
    emit('handleSearchQuery', tempQueryParams, 'request')
  }

  function exportExcel() {
    emit('exportExcel', 'listTop')
  }
  function handleDateChange(tableItem: TableItem) {
    //如果tableItem.query.name+LongTerm在tempQueryParams中存在，则将tempQueryParams[tableItem.query.name+LongTerm]的值设置false
    if (queryParams[tableItem.query?.name! + 'LongTerm']) {
      queryParams[tableItem.query?.name! + 'LongTerm'] = false
    }
  }

  // 处理日期范围变化，确保时间格式正确
  function handleDateRangeChange(tableItem: TableItem, value: any) {
    // 先调用原有的处理逻辑
    handleDateChange(tableItem)

    // 如果是datetimerange类型且有值，确保时间格式正确
    if (tableItem.query?.type === 'datetimerange' && value && Array.isArray(value) && value.length === 2) {
      const [startDate, endDate] = value

      if (startDate && endDate) {
        // 检查是否是从shortcuts选择的（时间部分已经设置好）
        const start = new Date(startDate)
        const end = new Date(endDate)

        // 如果开始时间是00:00:00且结束时间是23:59:59，说明是从shortcuts选择的，保持不变
        const isFromShortcuts =
          start.getHours() === 0 &&
          start.getMinutes() === 0 &&
          start.getSeconds() === 0 &&
          end.getHours() === 23 &&
          end.getMinutes() === 59 &&
          end.getSeconds() === 59

        if (isFromShortcuts) {
          // 如果有指定格式，按格式转换
          if (tableItem.query?.format) {
            const startFormatted = dayjs(start).format(tableItem.query.format)
            const endFormatted = dayjs(end).format(tableItem.query.format)
            queryParams[tableItem.query.name!] = [startFormatted, endFormatted]
          }
        }
      }
    }
  }
  const handleLongTerm = (tableItem: TableItem, e: any) => {
    //切换清空queryParams[tableItem.query?.name! ]
    queryParams[tableItem.query?.name!] = null
  }
  defineExpose({ queryParams, resetQuery, resetParams, searchQueryTemp, handleQuery })
</script>

<style scoped>
  .query-container {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    padding: 10px;
    /* width: 100%; */
    z-index: 8;
    background: #fff;
    border: 1px solid #dedfe6;
    border-radius: 4px;
    /* overflow: hidden; */
  }

  .query-box {
    display: flex;
    align-items: start;
    justify-content: space-between;
  }

  .query-container-left {
    flex: 1;
  }

  .query-container-right {
    width: 500px;
    padding: 0 20px;
  }

  .query-container :deep(.el-form-item) {
    margin-bottom: 10px;
    border: 1px solid #dedfe6;
    border-radius: 2px;
  }

  .query-container :deep(.el-input__wrapper),
  .query-container :deep(.el-select__wrapper),
  .query-container :deep(.el-date-editor.el-input) {
    border-radius: 0px;
    box-shadow: none;
    min-height: 25px;
  }

  .query-container :deep(.el-form-item__label) {
    background: #f0f0f0;
    color: #333;
    height: auto;
    align-items: center;
  }

  :deep(.el-select__selection) {
    flex-wrap: nowrap !important;
    margin-right: 45px !important;
  }

  :deep(.el-select__selected-item) {
    flex-wrap: nowrap !important;
  }

  :deep(.el-select__input) {
    min-width: 40px !important;
    max-width: 40px !important;
  }
</style>
