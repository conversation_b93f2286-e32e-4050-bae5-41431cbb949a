<!--
 * @Author: llm
 * @Date: 2025-06-17 18:26:32
 * @LastEditors: llm
 * @LastEditTime: 2025-06-27 11:27:50
 * @Description: 
-->
<template>
  <div>
    <el-dialog
      v-model="state.dialogVisible.visible"
      :title="state.dialogVisible.title"
      width="1100px"
      :close-on-click-modal="false"
      @close="handleClose"
      draggable
    >
      <el-scrollbar>
        <el-form
          :model="state.form"
          ref="formRef"
          :rules="state.rules"
          label-width="100px"
          style="min-width: 800px"
          :disabled="state.origin === 'edit' || state.origin === 'view'"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="调度单号" prop="dispatchNo">
                <!-- 远程模糊搜索下拉 -->
                <el-select
                  size="small"
                  v-model="state.form.dispatchNo"
                  clearable
                  filterable
                  placeholder="模糊搜索"
                  remote
                  :remote-method="remoteSelectMethod"
                  :loading="fuzzySelectLoading"
                  @change="handleDispatchChange"
                >
                  <el-option v-for="i in dispatchDataList" :key="i.value" :label="i.label" :value="i.value"></el-option>
                </el-select>

                <!-- <el-autocomplete
                  v-model="state.dispatchNo"
                  value-key="label"
                  style="width: 100%"
                  clearable
                  @keydown.space.prevent
                  @select="handleDispatchChange"
                  :placeholder="'模糊搜索'"
                  :fetch-suggestions="((query: any, cb: any) => remoteSelectMethod(query, cb)) as any"
                >
                  <template #default="{ item }">
                    <span class="link">{{ item.label }}</span>
                  </template>
                </el-autocomplete> -->
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <!-- 司机 -->
              <el-form-item label="司机" prop="driverName">
                <el-select v-model="state.form.driverName" placeholder="请选择司机" filterable @change="getDriverDataList">
                  <el-option v-for="i in state.driverDataList" :key="i.value" :label="i.label" :value="i.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <!-- 车牌号 -->
              <el-form-item label="车牌号" prop="vehicleNo">
                <!-- <el-select v-model="state.form.vehicleNo" placeholder="请选择车牌号" filterable @change="getVehicleDataList">
                  <el-option v-for="i in state.vehicleDataList" :key="i.label" :label="i.label" :value="i.value"></el-option>
                </el-select> -->
                <el-tree-select filterable  @change="getVehicleDataList" :placeholder="isvehicleDataList ? '请先选择司机' : '请选择车牌号'"  value-key="label" :disabled="isvehicleDataList" v-model=" state.form.vehicleNo" :data="state.vehicleDataList" :render-after-expand="false" style="width: 240px" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <!-- 维修日期 -->
              <el-form-item label="维修日期" prop="reportDate">
                <el-date-picker v-model="state.form.reportDate" type="date" placeholder="请选择维修日期" value-format="YYYY-MM-DD" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <!-- 总维修费 -->
              <el-form-item label="总维修费" prop="amount">
                <el-input :disabled="true" v-model="state.form.amount" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 个人垫付比例 -->
              <el-form-item label="个人垫付比例" prop="personalAdvance">
                <el-radio-group v-model="state.form.personalAdvance">
                  <el-radio :value="1">全额垫付</el-radio>
                  <el-radio :value="2">部分垫付</el-radio>
                  <el-radio :value="0">未垫付</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <!-- 维修地点 -->
              <el-form-item label="维修地点" prop="repairPlace">
                <el-input v-model="state.form.repairPlace" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <!-- 公司垫付金额 -->
              <el-form-item label="公司垫付金额" prop="companyAdvanceAmount">
                <el-input :disabled="true" v-model="state.form.companyAdvanceAmount" />
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="state.form.personalAdvance !== 0">
              <!-- 个人垫付金额 -->
              <el-form-item label="个人垫付金额" prop="personalAdvanceAmount">
                <el-input v-model="state.form.personalAdvanceAmount" :disabled="Number(state.form.personalAdvance) === 1" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <!-- 发票号 -->
              <el-form-item label="是否有发票" prop="hasInvoice">
                <el-radio-group v-model="state.form.hasInvoice">
                  <el-radio :value="1">是</el-radio>
                  <el-radio :value="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-show="state.form.hasInvoice === 1">
              <!-- 发票号 -->
              <el-form-item label="发票号" prop="invoiceNumber">
                <el-input v-model="state.form.invoiceNumber" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <!-- 凭证 -->
              <el-form-item label="凭证" prop="imageUrlList">
                <UploadImageComponent ref="uploadImageRef" :limit="20" :multiple="true" tip="" @fileData="handleFileData" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-divider border-style="dashed">维修项目</el-divider>
        <div class="mb-10px flex items-center">
          <el-button type="primary" @click="handleAddRepairItem" size="small" v-if="state.origin === 'add'">新增维修项目</el-button>
          <el-button type="danger" @click="handleBatchDelete" size="small" :disabled="selectionRows.length === 0" v-if="state.origin === 'add'"
            >批量删除</el-button
          >
          <el-button type="primary" @click="handleDownloadTemplate" size="small" v-if="state.origin === 'add'">模版下载</el-button>
          <el-upload
            class="ml-12px"
            ref="uploadFileRef"
            :auto-upload="false"
            :limit="1"
            :on-change="(file: any) => handleImportTemplate(file)"
            :show-file-list="false"
            action="#"
            :on-exceed="
              (files: any, fileList: any[]) => {
                handleExceed(files, fileList)
              }
            "
            v-if="state.origin === 'add'"
          >
            <el-button type="primary" size="small">模版导入</el-button>
          </el-upload>
        </div>
        <vxe-table
          border
          ref="tableRef"
          max-height="500"
          :data="tableData"
          size="small"
          :show-overflow="false"
          :show-footer="false"
          @checkbox-all="selectAllChangeEvent"
          @checkbox-change="selectChangeEvent"
        >
          <vxe-column type="checkbox" width="50" fixed="left"></vxe-column>
          <vxe-column type="seq" width="50" fixed="left"></vxe-column>
          <vxe-column v-for="item in tableConfig.tableItem" :key="item.name" :field="item.name" :title="item.label" align="center"></vxe-column>
          <vxe-column title="操作" width="120" fixed="right" align="center" v-if="state.origin === 'add' || state.origin === 'edit'">
            <template #default="{ row }">
              <el-button type="primary" @click="handleEdit(row)" size="small" link>编辑</el-button>
              <el-button type="danger" @click="handleDelete(row)" size="small" link v-if="state.origin === 'add'">删除</el-button>
            </template>
          </vxe-column>
        </vxe-table>
      </el-scrollbar>
      <template #footer v-if="state.origin === 'add' || state.origin === 'edit'">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </template>
    </el-dialog>
    <FormDialogComponent ref="formDialogRef" @addRepairItem="addRepairItem" :repairItemList="state.repairItemList" :isEdit="state.origin === 'edit'" />
  </div>
</template>
<script lang="ts" setup>
  import { ref, reactive, watch, nextTick } from 'vue'
  import UploadImageComponent from '@/components/UploadImageComponent/index.vue'
  import { genFileId, UploadProps, UploadRawFile, UploadUserFile } from 'element-plus'
  import { Colgroup, log, VxeTableEvents, VxeTableInstance } from 'vxe-table'
  import FormDialogComponent from './formDialogComponent.vue'
  import {
    getCommonOptionBusinessSelectApi,
    getOutFleetOrderDispatchOptionDispatchInfoApi,
    postOutFleetOrderRepairApi,
    putOutFleetOrderRepairApi,
  } from '@/api/outboundMaintenanceManagement'
  import {
    selectvehicleOption,
  } from '@/api/fleetManagement'
  import { debounce } from 'lodash'
  import { downloadCustomTemplate } from '@/api/outboundDispatchManagement'
  import { downloadFileGlobalFun } from '@/utils/common'
  import { importFileGlobalBtnUriFun, importFileGlobalFun } from '@/api/auth'
  const emit = defineEmits(['refresh'])
  const tableRef = ref<VxeTableInstance>()
  const uploadFileRef = ref()
  interface RowVO {
    id?: string
    idd?: string
    itemName?: string
    amount?: string
    remark?: string
    index?: number
  }
  const selectionRows = ref<RowVO[]>([])
  const uploadImageRef = ref()
  const formDialogRef = ref()
  const formRef = ref()
  const state = reactive({
    origin: 'add' as 'add' | 'edit' | 'view',
    dialogVisible: {
      visible: false,
      title: '',
    },
    rules: {
      driverName: [{ required: true, message: '请选择司机', trigger: 'change' }],
      vehicleNo: [{ required: true, message: '请选择车牌号', trigger: 'change' }],
      reportDate: [{ required: true, message: '请选择维修日期', trigger: 'change' }],
      personalAdvance: [{ required: true, message: '请选择个人垫付比例', trigger: 'change' }],
      imageUrlList: [{ required: true, message: '请上传凭证', trigger: 'change' }],
    },
    dispatchNo: '',
    form: {
      id: undefined,
      idd: undefined,
      dispatchNo: '',
      driverName: '',
      vehicleNo: '',
      reportDate: '',
      amount: 0,
      personalAdvance: 0,
      repairPlace: '',
      companyAdvanceAmount: 0,
      personalAdvanceAmount: 0,
      invoiceNumber: '',
      plateColor: '',
      hasInvoice: 0,
      items: [] as RowVO[],
      imageUrlList: [] as UploadUserFile[],
    },
    currentRowIndex: -1,
    driverDataList: [] as SelectOptions[],
    vehicleDataList: [] as SelectOptions[],
    repairItemList: [] as SelectOptions[],
  })

  // Add watch effects for payment calculations
  watch([() => state.form.personalAdvance, () => state.form.amount], ([newRatio, totalFee]) => {
    if (Number(newRatio) === 1) {
      // 全额垫付
      state.form.companyAdvanceAmount = 0
      state.form.personalAdvanceAmount = totalFee
    } else if (Number(newRatio) === 2) {
      // 部分垫付
      const total = Number(totalFee) || 0
      const personal = Number(state.form.personalAdvanceAmount) || 0
      state.form.companyAdvanceAmount = total - personal
    } else if (Number(newRatio) === 0) {
      // 未垫付
      state.form.companyAdvanceAmount = totalFee
      state.form.personalAdvanceAmount = 0
    }
  })
  let isvehicleDataList = ref(true)

  // Watch personal payment amount changes for partial payment calculations
  watch(
    () => state.form.personalAdvanceAmount,
    (newAmount) => {
      if (Number(state.form.personalAdvance) === 2) {
        // 个人垫付金额不能大于总维修费
        if (Number(newAmount) > Number(state.form.amount)) {
          state.form.personalAdvanceAmount = state.form.amount
          ElMessage.error('个人垫付金额不能大于总维修费')
          return
        }
        // 部分垫付
        const total = Number(state.form.amount) || 0
        const personal = Number(newAmount) || 0
        state.form.companyAdvanceAmount = total - personal
      }
    },
  )
  watch(
    () => state.dialogVisible.visible,
    (newVisible) => {
      if (newVisible) {
        getDriverDataList()
        getVehicleDataList()
        getRepairItemList()
      } else {
        state.driverDataList = []
        state.vehicleDataList = []
        state.repairItemList = []
      }
    },
  )
  const getDispatchDataList = async (query: string) => {
    const res = await getCommonOptionBusinessSelectApi({
      label: 'name',
      dataSource: '调度单-扩展',
      value: 'code',
      fuzzy: true,
      keyword: query,
    })
    return res.data
  }
  const getVehicleDataList = async (driverUserId:string='') => {
    const res = await selectvehicleOption({
      limitInfo: '',
      label: 'name,plateColor',
      value: 'vehicleNo',
      dataSource: '车队-车辆',
      fuzzy: false,
      driverUserId,
    })
    console.log(res.data,'ppppp');
    
    state.vehicleDataList = res.data as SelectOptions[]
  }
  const getDriverDataList = async (val:any = '') => {
    const res = await getCommonOptionBusinessSelectApi({
      limitInfo: '',
      label: 'driverName,driverMobile',
      value: 'driverName',
      dataSource: '车队-司机',
      fuzzy: false,
      keyId: 'driverId'
    })
    console.log(val);
    if(val != ''){
      let driverId = (state.driverDataList.find(item => item.value === val) as any).keyId
      if(driverId){
       isvehicleDataList.value = false
       state.form.vehicleNo = ''
       await getVehicleDataList(driverId)
      }
    }
    // await getVehicleDataList()
    state.driverDataList = res.data as SelectOptions[]
  }
  //维修项目列表
  const getRepairItemList = async () => {
    const res = await getCommonOptionBusinessSelectApi({
      label: 'name',
      value: 'name',
      dataSource: '维修项目',
    })
    state.repairItemList = res.data as SelectOptions[]
  }
  const tableConfig = reactive<TableConfig>({
    tableItem: [
      { name: 'itemName', label: '维修项目' },
      { name: 'amount', label: '维修费用' },
      { name: 'remark', label: '备注' },
    ],
  })
  const tableData = ref<RowVO[]>([])
  const dispatchDataList = ref<SelectOptions[]>([])
  const fuzzySelectLoading = ref(false)
  // 获取上传图片列表
  const getUploadImageList = () => {
    return uploadImageRef.value.uploadImageList
  }

  //设置上传图片列表
  const setUploadImageList = (list: UploadUserFile[]) => {
    uploadImageRef.value.uploadImageList = list || []
  }
  const handleClose = () => {
    setUploadImageList([])
    tableData.value = []
    formRef.value.resetFields()
  }
  // 取消
  const handleCancel = () => {
    handleClose()
    state.dialogVisible.visible = false
  }
  // 提交
  const handleSubmit = debounce(async () => {
    await formRef.value.validate()
    state.form.items = tableData.value.map((item) => {
      return item
    })
    state.form.imageUrlList = getUploadImageList()
    
    let splitstr = state.form.vehicleNo.split('-')
    state.form.vehicleNo = splitstr[0]
    state.form.plateColor = splitstr[1]

    console.log(state.form,splitstr)

    let params = null
    if (state.origin === 'add') {
      state.form.id = undefined
    }
    if (!state.form.id) {
      params = state.form
      await postOutFleetOrderRepairApi(params)
    } else {
      params = state.form.items
      await putOutFleetOrderRepairApi(params)
    }
    ElMessage.success('操作成功')
    state.form.id = undefined
    tableData.value = []
    formRef.value.resetFields()
    emit('refresh')
    handleCancel()
  }, 1000)
  const handleDispatchChange = async (dispatchNo: string) => {
    if (!dispatchNo) {
      state.form.driverName = ''
      state.form.vehicleNo = ''
      return
    }
    const { data } = await getOutFleetOrderDispatchOptionDispatchInfoApi({ dispatchNo })
    state.form.driverName = data.driverName || ''
    state.form.vehicleNo = data.vehicleNo || ''
  }
  // const remoteSelectMethod = async (query: string, cb: (suggestions: any) => void, key: string) => {
  //   if (query) {
  //     const data = await getDispatchDataList(query)
  //     dispatchDataList.value = data as SelectOptions[]
  //     cb(data)
  //   } else {
  //     cb([])
  //   }
  // }
  const remoteSelectMethod = async (query: string) => {
    if (query) {
      const data = await getDispatchDataList(query)
      dispatchDataList.value = data as SelectOptions[]
      return data
    } else {
      return []
    }
  }

  const selectAllChangeEvent: VxeTableEvents.CheckboxAll<RowVO> = ({ checked }) => {
    const $table = tableRef.value
    if ($table) {
      const records = $table.getCheckboxRecords()
      selectionRows.value = records
    }
  }

  const selectChangeEvent: VxeTableEvents.CheckboxChange<RowVO> = ({ checked }) => {
    const $table = tableRef.value
    if ($table) {
      const records = $table.getCheckboxRecords()
      selectionRows.value = records
    }
  }
  const handleAddRepairItem = () => {
    state.currentRowIndex = -1
    formDialogRef.value.state.dialogVisible.visible = true
    formDialogRef.value.state.dialogVisible.title = '新增维修项目'
  }
  const handleBatchDelete = () => {
    //删除tableData中selectionRows.value的行
    tableData.value = tableData.value.filter((item) => !selectionRows.value.includes(item))
    selectionRows.value = []
  }
  const handleDownloadTemplate = async () => {
    const res = await downloadCustomTemplate('/logistics/api/out/fleet/repair/item/template')
    downloadFileGlobalFun(res)
  }
  const handleImportTemplate = (file: any) => {
    importFileGlobalBtnUriFun(file.raw, '/logistics/api/out/fleet/repair/item/resolve').then(async (res) => {
      ElMessage.success('导入成功')
      tableData.value = res.data.map((item: any, index: number) => {
        return {
          id: Date.now().toString() + index,
          idd: Date.now().toString() + index,
          itemName: item.itemName,
          amount: item.amount,
          remark: item.remark,
        }
      })
      state.form.amount = tableData.value.reduce((sum, item) => sum + Number(item.amount), 0)
    })
  }
  const addRepairItem = (form: any) => {
    const formData = JSON.parse(JSON.stringify(form))
    const newTableData = [...tableData.value]

    if (formData.id) {
      // 如果有id，查找并更新对应的项目
      const index = newTableData.findIndex((item) => item.id === formData.id)
      if (index !== -1) {
        newTableData[index] = {
          ...formData,
        }
      } else {
        // 如果找不到对应id的项目，添加为新项目
        newTableData.unshift({
          ...formData,
        })
      }
    } else {
      // 如果没有id，作为新项目添加
      newTableData.unshift({
        ...formData,
        id: Date.now().toString(), // 为新项目生成一个唯一id
      })
    }

    // 通过重新赋值来确保响应式更新
    tableData.value = newTableData

    // 计算总维修费
    state.form.amount = newTableData.reduce((sum, item) => sum + Number(item.amount), 0)
  }
  const handleEdit = (row: any) => {
    state.currentRowIndex = row.index
    formDialogRef.value.state.dialogVisible.visible = true
    formDialogRef.value.state.dialogVisible.title = '编辑维修项目'
    formDialogRef.value.state.form = JSON.parse(JSON.stringify(row))
  }
  const handleDelete = (row: any) => {
    tableData.value.splice(row.index, 1)
  }
  const handleExceed: UploadProps['onExceed'] = (files) => {
    uploadFileRef.value!.clearFiles()
    const file = files[0] as UploadRawFile
    file.uid = genFileId()
    uploadFileRef.value!.handleStart(file)
  }
  const handleFileData = (file: any) => {
    state.form.imageUrlList = file
    formRef.value.validateField('imageUrlList')
  }
  defineExpose({
    state,
    setUploadImageList,
    tableData,
    remoteSelectMethod,
  })
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    border-top-color: #e89e42;
  }

  :deep(.el-divider__text) {
    color: #e89e42;
  }
</style>
