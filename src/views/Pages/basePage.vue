/* * @Author: llm * @Date: 2024-05-16 15:48:41 * @LastEditors: llm * @LastEditTime: 2024-05-23 12:14:05 * @Description: desc */
<template>
  <div class="app-container">
    <!-- 顶部搜索区域 -->
    <el-card v-show="topQueryConfig.tableItem.filter((item) => item.query).length > 0" class="top-query" shadow="never" style="margin-bottom: 20px">
      <div ref="content" :class="{ statusText: state.status }" :style="{ height: state.status ? state.textHeight : 'auto' }">
        <topQueryGroupComponent
          ref="topQueryGroupComponentRef"
          :query-permission-group="topQueryConfig.tableItem"
          :show-search="showSearch"
          @handleQueryBottomList="handleQueryBottomList"
          @handleSearchQuery="handleSearchQuery"
          @queryParams="getQueryData"
        />
      </div>
      <div v-if="state.isShowMore" style="padding-bottom: 20px; color: #606266; text-align: center">
        <div class="more">
          <el-button :icon="state.status ? 'ArrowDown' : 'ArrowUp'" link type="" @click="moreClick">
            {{ state.status ? '展开更多' : '收起' }}
          </el-button>
        </div>
      </div>
    </el-card>
    <el-card>
      <template #header>
        <!-- 按钮组 -->
        <button-group-component
          ref="topButtonGroupComponent"
          :buttonPermissionGroup="buttonPermissionGroup"
          :downloadButtonLoading="downloadButtonLoading"
          :exportButtonLoading="exportButtonLoading"
          :ids="customerIds"
          :importButtonLoading="importButtonLoading"
          @addItem="addItem"
          @downloadExcelTemplate="downloadExcelTemplate"
          @editItem="editItem"
          @exportExcel="exportExcelFun"
          @handleDelete="handleDelete"
          @importExcel="importExcelFun"
          @addLine="addLineFun"
          @showMenuDialog="showRightBottomMenuDialog"
        />
        <!-- @waitingAllocateVinWarning="waitingAllocateVinWarning" -->
      </template>
      <div>
        <el-row>
          <el-col :span="4">
            <!-- 左侧栏 -->
            <div class="left-sidebar">
              <left-sidebar-component
                ref="leftSideBarListRef"
                :checkedItem="checkedItem"
                :leftSideBarList="leftSideBarList"
                :leftSideBarListTotal="leftSideBarListTotal"
                :listName="listName"
                :queryParams="leftListQueryParams"
                @changeStatus="changeStatus"
                @getItem="getItem"
                @handleQueryLeftSideBarList="handleQueryLeftSideBarList"
                @leftSideBarListNameSearch="leftSideBarListNameSearch"
              />
            </div>
          </el-col>
          <el-col :span="20">
            <!-- 右侧区域 -->
            <div>
              <div>
                <right-top-component
                  ref="rightTopRef"
                  :descriptionsConfig="tableConfig.tableItem"
                  :info="currentItem"
                  @handleStatusChange="handleStatusChange"
                />
              </div>
              <div>
                <right-bottom-component
                  ref="rightBottomRef"
                  :topButtonPermissionGroup="buttonPermissionGroup"
                  :bottomButtomPermissionGroup="bottomButtomPermissionGroup"
                  :bottomTabPermissionGroup="bottomTabPermissionGroup"
                  :detailData="detailData"
                  :requestUri="btnRequestUri ?? requestUri!"
                  :showAllSelection="showAllSelection"
                  @refreshPageTableColumn="refreshPageTableColumn"
                  @addItem="addBottomItem"
                  @changeTab="changeTab"
                  @defaultHandleTable="defaultHandleTable"
                  @deleteItem="deleteItemFun"
                  @downloadExcelTemplate="downloadButtomTabExcelTemplate"
                  @editBottomItem="editBottomItem"
                  @exportBottomTabListExcel="exportBottomTabListExcel"
                  @handleDeleteBottomListItem="handleDeleteBottomListItem"
                  @handleQueryBottomList="handleQueryBottomList"
                  @importButtomTabExcelFun="importButtomTabExcelFun"
                  @showRowMenuDialog="showRowMenuDialog"
                  @switchBottomListStatus="switchBottomListStatus"
                  @addSubLine="addSubLine"
                  @showMenuDialog="showMenuDialog"
                />
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="4">
            <pagination
              v-if="leftSideBarListTotal > queryParams.limit!"
              v-model:limit="queryParams.limit"
              v-model:page="queryParams.page"
              v-model:total="leftSideBarListTotal"
              :background="false"
              :pager-count="3"
              :small="true"
              layout="prev, pager, next"
              @pagination="handleQueryLeftSideBarList"
            />
          </el-col>
          <el-col :span="20">
            <pagination
              v-if="currentTabUri != 'tms/company/switch/config/loadVin/detailById'"
              v-model:limit="bottomListQueryParams.limit"
              v-model:page="bottomListQueryParams.page"
              v-model:total="rightBottomListTotal"
              @pagination="handleQueryBottomList"
            />
          </el-col>
        </el-row>
      </div>
    </el-card>
    <!-- 新增弹窗 -->
    <form-dialog
      v-if="dialog.visible"
      ref="formDialogRef"
      :btnMenu="btnMenu"
      :dataColumn="operationColumn"
      :dialog="dialog"
      :isEdit="isEdit"
      :requestUri="btnRequestUri ?? requestUri"
      @clearFormColumn="clearFormColumn"
      @closeDialog="closeDialog"
      @formData="getFormData"
      @handleSubmit="handleSubmit"
    />
    <!-- 待配板划分设置弹窗 -->
    <!-- <WaitingAllocationDialogComponent ref="waitingProjectDialogRef" :waitingFormData="setupData" /> -->
    <!-- 列表项详情弹窗 -->
    <el-dialog
      :draggable="true"
      v-model="detailDialogVisible.visible"
      :title="detailDialogVisible.title"
      align-center
      destroy-on-close
      :width="detailDialogVisible.dialogWidth ?? '80%'"
    >
      <el-scrollbar class="formClass" max-height="90vh">
        <BasePage1 />
      </el-scrollbar>
    </el-dialog>
    <!--    新增线路弹窗    -->
    <AddLineFormDialogComponent
      v-if="addLineDialog.visible"
      ref="formDialogRef"
      :btnMenu="btnMenu"
      :dataColumn="operationColumn"
      :dialog="addLineDialog"
      :isEdit="isEdit"
      :requestUri="btnRequestUri ?? requestUri"
      @clearFormColumn="clearFormColumn"
      @closeDialog="closeDialog"
      @formData="getFormData"
      @handleSubmit="handleSubmit"
    />
    <el-dialog
      v-model="subLineDialog.visible"
      :title="subLineDialog.title"
      width="800px"
      :draggable="true"
      :close-on-click-modal="false"
      @closed="closeDialog1"
    >
      <el-scrollbar max-height="60vh" class="formClass">
        <subLineForm ref="subLineFormRef" :requestUri="btnRequestUri ?? requestUri" :relationId="currentItem?.relationId" />
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <div>
            <el-button type="primary" @click="handleSubLineSubmit" :data-uri="btnRequestUri ?? requestUri">确定</el-button>
            <el-button @click="closeDialog1()">取 消</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import BasePage1 from '@/views/Pages/basePage1.vue'
  import buttonGroupComponent from '@/components/TopButtonGroupComponent/index.vue' //操作按钮
  import formDialog from '@/components/FormDialogComponent/index.vue' //表单弹窗
  import leftSidebarComponent from '@/views/customerCenter/components/leftSidebarComponent.vue' //左侧栏
  import rightTopComponent from '@/views/customerCenter/components/rightTopComponent.vue' //右上部
  import rightBottomComponent from '@/views/customerCenter/components/rightBottomComponent.vue' //右下部
  // import WaitingAllocationDialogComponent from '@/components/OtherFormDialogComponent/WaitingAllocationDialogComponent/index.vue'; //待配板划分设置弹窗
  import AddLineFormDialogComponent from '@/components/OtherFormDialogComponent/AddLineFormDialogComponent/index.vue' //新增线路弹窗
  import subLineForm from '@/components/OtherFormDialogComponent/AddLineFormDialogComponent/subLineForm.vue' //新增联运线路
  import { CustomerVO } from '@/api/customerCenter/customerBaseData/types'
  import {
    composeRequestParams,
    downloadFileGlobalFun,
    getcurrentUserMenuColumnlist,
    getSelectOptions,
    resetFormGlobalFun,
    switchChangeGlobalFun,
  } from '@/utils/common'
  import { UserForm } from '@/api/authorityManagement/CorporateAccountManagement/PersonalManagement/types'
  import {
    addItemApi,
    batchDeleteApi,
    bottomTableList,
    deleteItemApi,
    downloadTemplate,
    exportExcel,
    getListPage,
    globalDownloadTemplate,
    globalExportExcel,
    importFileGlobalBtnUriFun,
    importFileGlobalFun,
    updateItemApi,
  } from '@/api/auth'
  import { FormColumn, LeftSideQueryParams } from '@/types/global'
  import { getMenuCount, globalRequestApi, globalRequestUrlApi } from '@/api/planManagement'
  import { useSideBarStore } from '@/store/modules/sideBar'
  // 状态管理依赖
  import { useFormStore } from '@/store/modules/form.js'
  import { useSettingsStore } from '@/store/modules/settings'
  import { dayjs } from 'element-plus'
  import { postAddSubLineApi } from '@/api/customerCenter/customerBaseData'
  import defaultSettings from '@/settings'

  const sideBarStore = useSideBarStore()
  const settingsStore = useSettingsStore()
  const formStore = useFormStore()
  const { routerParams } = storeToRefs(formStore)
  const { proxy }: any = getCurrentInstance()
  //优先使用列表中的携带的参数，再使用地址栏参数
  const routeParams = proxy.$sideBarStore.$state.btnMenuQuery ? proxy.$sideBarStore.$state.btnMenuQuery : routerParams.value
  const topQueryGroupComponentRef = ref()
  const state = reactive({
    isShowMore: false, // 控制展开更多的显示与隐藏
    textHeight: '', // 框中内容的高度
    status: false, // 内容状态是否打开
  })
  const showSearch = ref(false) //顶部搜索区域是否展示查询重置按钮
  const content = ref()
  const moreClick = () => {
    state.status = !state.status
  }

  /**
   * 获取待配板数据
   */
  const detailData = ref<any>()

  /**
   * 按钮下的表单
   */
  const operationColumn = ref<any>()
  /**
   * 查看列表项详情弹窗
   */
  const detailDialogVisible = reactive<DialogOption>({
    visible: false,
    title: '详情',
    dialogWidth: '80%',
  })

  /**
   * 按钮的请求地址前缀
   */
  const btnRequestUri = ref<string | null>()

  /**
   * 是否编辑状态
   */
  const isEdit = ref<boolean>()
  /**
   * 按钮组
   */
  const btnGroup = ref<MenuVO[]>([])
  /**
   * 客户列表查询条件
   */
  const queryParams = reactive<any>({ page: 1, limit: defaultSettings.globalLimit })
  /**
   * 左侧列表查询条件
   */
  const leftListQueryParams = reactive<LeftSideQueryParams | any>({
    page: 1,
    limit: defaultSettings.globalLimit,
    name: undefined,
    enable: undefined,
  })
  /**
   * 左侧菜单列表总条数
   */
  const leftSideBarListTotal = ref<number>(0)
  /**
   * 右侧列表总条数
   */
  const rightBottomListTotal = ref<number>(0)
  /**
   * 底部列表查询条件
   */
  const bottomListQueryParams = reactive<any>({
    page: 1,
    limit: defaultSettings.globalLimit,
  })
  const topButtonGroupComponent = ref()
  /**
   * 左侧选中列表项
   */
  const checkedItem = ref<string[]>()
  /**
   * form 表单弹窗
   */
  const formDialogRef = ref()
  /**
   * 新增线路表单弹窗
   */
  const addLineFormDialogRef = ref()
  /**
   * 按钮组权限
   */
  const buttonPermissionGroup = ref<MenuVO[]>()
  /**
   * 右下区域tab
   */
  const bottomTabPermissionGroup = ref<MenuVO[]>([])
  /**
   * 右下区域tab中的按钮组
   */
  const bottomButtomPermissionGroup = ref<MenuVO[]>([])
  /**
   * 左侧栏列表
   */
  const leftSideBarListRef = ref()
  /**
   * 右上区域
   */
  const rightTopRef = ref()
  /**
   * 右下区域
   */
  const rightBottomRef = ref()
  /**
   * 选中的tab
   */
  const currentTab = ref<MenuVO>()
  /**
   * 选中的当前行
   */
  const currentItem = ref<TableItem>()
  /**
   * 左侧列表
   */
  const leftSideBarList = ref<CustomerVO[]>([])
  /**
   * 用于显示在左侧栏中的列表名称
   */
  const listName = ''
  /**
   * 勾选需要删除的客户ids
   */
  const customerIds = ref<string[]>()
  /**
   * 当前菜单的uri
   */
  const currentMenuUri = ref<string>()
  /**
   * 当前tab的uri
   */
  const currentTabUri = ref<string>()
  /**
   * 请求地址前缀
   */
  const requestUri = ref<string>('')
  /**
   * 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   */
  const buttonPosition = ref<string>()
  /**
   * 导出加载动画
   */
  const exportButtonLoading = ref<boolean>()
  /**
   * 导入加载动画
   */
  const importButtonLoading = ref<boolean>()
  /**
   * 下载加载动画
   */
  const downloadButtonLoading = ref<boolean>()
  /**
   * 弹窗
   */
  const dialog = reactive<DialogOption>({
    visible: false,
  })
  /**
   * 新增线路弹窗
   */
  const addLineDialog = reactive<DialogOption>({
    visible: false,
  })
  /**
   * 新增联运线路弹窗
   */
  const subLineDialog = reactive<DialogOption>({
    visible: false,
  })
  /**
   * 控制多选复选框按钮显示隐藏
   */
  const showAllSelection = ref<boolean>(true)
  /**
   * menu
   */
  const btnMenu = ref<MenuVO>()
  /**
   * meta
   */
  const metaInfo = ref<MetaVO>()
  /**
   * 列表配置项
   */
  const tableConfig = reactive<TableConfig>({
    showHandleSelection: true,
    showSort: true,
    tableItem: [],
    operation: {
      label: '操作',
      items: [],
      width: '120px',
    },
  })
  /**
   * 初始化表单数据
   */
  const clearFormColumn = () => {
    operationColumn.value = []
    dialog.title = ''
  }
  /**
   * 刷新数据列
   */
  const refreshPageTableColumn = async () => {
    await getPermission(proxy.$sideBarStore.$state.menuId)
    await getList()
  }
  /**
   * 顶部搜索配置项
   */
  const topQueryConfig = reactive<TableConfig>({ tableItem: [] })
  onMounted(async () => {
    document.title = settingsStore.systemName
    //默认将左侧菜单第一条数据存到pinia中，初始化
    formStore.$patch((state) => {
      state.selectLeftTreeRows.map((item, index) => {
        if (state.selectLeftTreeRows[index].menuId === proxy.$sideBarStore.$state.menuId) {
          state.selectLeftTreeRows[index].selectLeftTreeRow = null
        }
      })
    })
    await getPermission(proxy.$sideBarStore.$state.menuId)
    await getList()
  })
  /**
   * 获取菜单下的权限
   * @param menuId 菜单id
   */
  const getPermission = async (menuId: string) => {
    return new Promise<void>(async (resolve) => {
      const { children, meta }: { children: MenuVO[]; meta: MetaVO } = await getcurrentUserMenuColumnlist(menuId)
      metaInfo.value = meta
      //控制多选复选框按钮显示隐藏
      showAllSelection.value = meta.ext?.tableMultiSelect ?? true
      //获取当前菜单分页条数
      queryParams.limit = meta.ext?.pageLimit || defaultSettings.globalLimit
      btnGroup.value = children
      //获取顶部按钮组
      buttonPermissionGroup.value = children.filter((item: MenuVO) => item.meta?.type !== 3)
      //获取右下区域tab组
      bottomTabPermissionGroup.value = children.filter((item: MenuVO) => item.meta?.type === 3)
      //顶部搜索区域是否展示查询重置按钮
      showSearch.value = buttonPermissionGroup.value.some((item: MenuVO) => item.meta?.purpose === 'search')
      //如果bottomTabPermissionGroup.length>0,取第一个tab的menuId,获取数据列
      if (bottomTabPermissionGroup.value.length > 0 && bottomTabPermissionGroup.value[0].menuId) {
        //默认当前tab为tab中的第一项
        currentTab.value = bottomTabPermissionGroup.value[0]
        await getTabPermissionGroup(bottomTabPermissionGroup.value[0].menuId)
      }
      //获取form表单数据列
      const dataColumn: any = await getSelectOptions(meta.dataColumn, null, '', 'topQuerySelect')
      currentMenuUri.value = meta.uri
      topQueryConfig.tableItem = tableConfig.tableItem = rightTopRef.value.tableConfig.tableItem = dataColumn
      dataColumn.forEach((item: TableItem) => {
        //由于服务端图片校验规则下发后是字符串，不能转换，所以需要处理
        if (item.form?.imageOption && item.form?.imageOption!.required) {
          //图片校验
          const validateImage = (rule: any, value: any, callback: any) => {
            //验证器
            if (!formDialogRef.value.formData[item.form?.name!]) {
              //为true代表图片在  false报错
              callback(new Error('请上传图片'))
            } else {
              callback()
            }
          }
          item.form.rules = [{ required: true, validator: validateImage, trigger: 'change' }]
        }
        //如果item.defaultValue存在，则给表单赋值
        if (item.query?.defaultValue) {
          queryParams[item.query?.name!] = item.query?.defaultValue
          queryParams[item.query?.name!] = topQueryGroupComponentRef.value.queryParams[item.query?.name!] = item.query?.defaultValue
        }
        // 存在默认值
        if (item.query?.showDefaultDate && item.query?.format) {
          queryParams[item.query?.name!] = dayjs().format(item.query?.format)
          topQueryGroupComponentRef.value.queryParams[item.query?.name!] = dayjs().format(item.query?.format)
        }
      })
      //如果meta.operation=false 则不展示列表右侧操作列
      if (!meta.operation) {
        tableConfig.operation = undefined
      }
      //遍历routeParams对象，queryParams[key] = routeParams[key]
      for (const key in routeParams) {
        if (Object.prototype.hasOwnProperty.call(routeParams, key)) {
          //如果属性中包含range字符串，则把首字母大写，然后拆分2个，分别拼上start 和 end ,并删除原有属性
          if (key!.indexOf('Range') !== -1 && (routeParams[key] as any)) {
            const startName = 'start' + key.charAt(0).toUpperCase() + key.slice(1)
            const endName = 'end' + key.charAt(0).toUpperCase() + key.slice(1)
            queryParams[startName] = routeParams[key]![0]
            queryParams[endName] = routeParams[key]![1]
            delete queryParams[key]
          } else {
            queryParams[key] = routeParams[key]
          }
          topQueryGroupComponentRef.value.queryParams[key] = routeParams[key]
          //初始化搜索条件
          // formStore.setSearchParams({});
        }
      }
      // 计算展开更多内容超出显示
      await nextTick(() => {
        // 这里具体行数可依据需求自定义
        let lineHeight = 50 * 2
        state.textHeight = `${lineHeight}px`

        if (content.value.offsetHeight > lineHeight) {
          state.isShowMore = true
          state.status = true
        } else {
          state.isShowMore = false
          state.status = false
        }
      })
      resolve()
    })
  }
  /**
   * 切换tab 重新获取权限
   * @param menuId 菜单id
   */
  const getTabPermissionGroup = async (menuId: string) => {
    const { children, meta }: { children: MenuVO[]; meta: MetaVO } = await getcurrentUserMenuColumnlist(menuId)
    bottomButtomPermissionGroup.value = children
    currentTabUri.value = meta.uri

    //获取form表单数据列
    const dataColumn: FormColumn[] = await getSelectOptions(meta.dataColumn, null, '', 'topQuerySelect')
    rightBottomRef.value.tableConfig.tableItem = dataColumn
    rightBottomRef.value.tableConfig.isKey = !rightBottomRef.value.tableConfig.isKey
    dataColumn.forEach((item: TableItem, index: number) => {
      //由于服务端图片校验规则下发后是字符串，不能转换，所以需要处理
      if (item.form?.imageOption && item.form?.imageOption!.required) {
        //图片校验
        const validateImage = (rule: any, value: any, callback: any) => {
          //验证器
          if (!formDialogRef.value.formData[item.form?.name!]) {
            //为true代表图片在  false报错
            callback(new Error('请上传图片'))
          } else {
            callback()
          }
        }
        const imageRules = [{ required: true, validator: validateImage, trigger: 'change' }]
        item.form.rules = imageRules
      }
      //如果item.defaultValue存在，则给表单赋值
      if (item.query?.defaultValue) {
        queryParams[item.query?.name!] = topQueryGroupComponentRef.value.queryParams[item.query?.name!] = item.query?.defaultValue
      }
    })
    rightBottomRef.value.tableConfig.operation = meta.operation
    //遍历routeParams对象，queryParams[key] = routeParams[key]
    for (const key in routeParams) {
      if (Object.prototype.hasOwnProperty.call(routeParams, key)) {
        //如果属性中包含range字符串，则把首字母大写，然后拆分2个，分别拼上start 和 end ,并删除原有属性
        if (key!.indexOf('Range') !== -1 && (routeParams[key] as any)) {
          const startName = 'start' + key.charAt(0).toUpperCase() + key.slice(1)
          const endName = 'end' + key.charAt(0).toUpperCase() + key.slice(1)
          queryParams[startName] = routeParams[key]![0]
          queryParams[endName] = routeParams[key]![1]
          delete queryParams[key]
        } else {
          queryParams[key] = routeParams[key]
        }
        topQueryGroupComponentRef.value.queryParams[key] = routeParams[key]
        //初始化搜索条件
        // formStore.setSearchParams({});
      }
    }
  }
  /**
   * 获取左侧数据列表
   */
  const getList = async () => {
    leftSideBarListRef.value.listTreeLoading = true
    getListPage(leftListQueryParams, currentMenuUri.value!)
      .then(async (res) => {
        const { data } = res
        leftSideBarList.value = data.rows
        leftSideBarListTotal.value = data.total
        if (data.rows.length > 0 && data.rows[0].id) {
          //默认customerIds为客户列表的第一条数据id
          customerIds.value = [data.rows[0].id]
          //默认获取当前第一条数据
          currentItem.value = data.rows[0]
          //默认将左侧菜单第一条数据存到pinia中，用于后面数据调用里面的属性
          formStore.$patch((state) => {
            state.selectLeftTreeRows.map((item) => {
              if (item.menuId === proxy.$sideBarStore.$state.menuId) {
                item.selectLeftTreeRow = data.rows[0]
              }
            })
          })
          if (bottomTabPermissionGroup.value.length > 0 && bottomTabPermissionGroup.value[0].meta?.uri) {
            requestUri.value = bottomTabPermissionGroup.value[0].meta?.uri
            //默认获取当前客户下的用户列表
            getBottomTableList(data.rows[0].id, currentTabUri.value!)
          }
        } else {
          currentItem.value = {}
          rightBottomRef.value.tableData = []
          rightBottomRef.value.total = rightBottomListTotal.value = 0
          rightBottomRef.value.listLoading = false
          detailData.value = {} //清空配板设置数据
        }
        leftSideBarListRef.value.listTreeLoading = false
        //重新获取form表单数据列，刷新筛选下拉项
        topQueryConfig.tableItem = await refreshQuerySelectOptions()
      })
      .catch((err) => {
        leftSideBarListRef.value.listTreeLoading = false
      })
  }
  /**
   * 更新查询条件下拉
   */
  const refreshQuerySelectOptions: any = async () => {
    return new Promise(async (resolve, reject) => {
      //重新获取form表单数据列，刷新筛选下拉项
      const dataColumn: FormColumn[] = await getSelectOptions(metaInfo.value!.dataColumn, null, '', 'querySelect')
      resolve(dataColumn)
    })
  }
  /**
   * 更新表单下拉
   */
  const refreshFormSelectOptions = async (dataColumn: FormColumn[]) => {
    //重新获取form表单数据列，刷新筛选下拉项
    return new Promise(async (resolve, reject) => {
      const newDataColumn = getSelectOptions(dataColumn, null, '', 'formSelect')
      resolve(newDataColumn)
    })
  }
  /**
   * 查询选中的客户详情
   */
  const getItem = async (row: TableItem) => {
    currentItem.value = row
    //ids 赋值 为当前客户id
    customerIds.value = [row.id!]
    if (currentTab.value?.menuId) {
      await getTabPermissionGroup(currentTab.value?.menuId)
    }
    if (bottomTabPermissionGroup.value.length > 0 && bottomTabPermissionGroup.value[0].meta?.uri && row.id) {
      //默认获取当前客户下的用户列表
      getBottomTableList(row.id, currentTab.value?.meta ? currentTab.value?.meta?.uri! : bottomTabPermissionGroup.value[0].meta?.uri)
    }
  }

  /**
   * 关闭弹窗
   */
  function closeDialog() {
    dialog.visible = false
    addLineDialog.visible = false
    resetForm()
  }
  /**
   * 关闭弹窗
   */
  function closeDialog1() {
    subLineDialog.visible = false
    subLineFormRef.value.formData.tableData1 = []
  }

  /**
   * 重置表单
   */
  function resetForm() {
    formDialogRef.value.resetForm()
  }

  /**
   * 修改侧边栏列表状态
   */
  const handleStatusChange = async (row: { [key: string]: any }) => {
    await switchChangeGlobalFun(row.enable, row.id, currentMenuUri.value!)
      .then(async () => {
        //修改成功后刷新列表
        await getList()
      })
      .catch(() => {
        //取消切换，恢复状态
        row.enable = row.enable !== true
      })
  }
  /**
   * 启用禁用底部列表项状态
   */
  const switchBottomListStatus = async (row: UserForm) => {
    await switchChangeGlobalFun(row.enable!, row.id, currentTabUri.value!)
      .then(async () => {
        //修改成功后刷新列表
        getBottomTableList(currentItem.value?.id!, currentTabUri.value!)
      })
      .catch(() => {
        //取消切换，恢复状态
        row.enable = row.enable !== true
      })
  }
  /**
   * 获取table列表数据
   * @param relationId id
   * @param uri 请求地址
   * @param isResetQuery 是否重置查询条件
   */
  const getBottomTableList = (relationId: string, uri: string, isResetQuery: boolean = false) => {
    rightBottomRef.value.listLoading = true
    if (isResetQuery) {
      //循环bottomListQueryParams删除bottomListQueryParams 所有的参数
      for (let key in bottomListQueryParams) {
        if (key !== 'page' && key !== 'limit') {
          delete bottomListQueryParams[key]
        }
      }
      bottomListQueryParams.page = 1
    }
    bottomListQueryParams.relationId = relationId
    if (uri === 'tms/company/switch/config/loadVin/detailById') {
      getConfigurableData(uri)
    } else {
      bottomTableList(bottomListQueryParams, uri)
        .then((res) => {
          const { data } = res
          rightBottomRef.value.tableData = data.rows
          rightBottomRef.value.pageSummary = data.pageSummary
          rightBottomRef.value.totalSummary = data.totalSummary
          rightBottomRef.value.total = rightBottomListTotal.value = data.total
          rightBottomRef.value.listLoading = false
        })
        .catch((err) => {
          rightBottomRef.value.listLoading = false
        })
    }
  }
  /**
   * 获取表单项，用于表单中级联操作
   * @param formData 表单项
   * @param dependOn 当前修改的依赖项下拉
   */
  const getQueryData = async (formData: any, dependOn: string) => {
    //获取form表单数据列
    const newOperationColumn = await getSelectOptions(metaInfo.value!.dataColumn, formData, dependOn, 'topQuerySelect')
  }
  /**
   * 切换tab
   * @param tabItem
   */
  const changeTab = (tabItem: MenuVO) => {
    // 传参获取storeData tabItem.meta.ext.needStore
    //初始化page
    bottomListQueryParams.page = 1
    if (tabItem.meta?.ext?.needStore) {
      //将storeData中的参数拼接到bottomListQueryParams
      for (let key in formStore.storeFormParams) {
        if (key !== 'page' && key !== 'limit') {
          bottomListQueryParams[key] = formStore.storeFormParams[key]
        }
      }
    }
    rightBottomRef.value.listQueryParams.page = 1
    rightBottomRef.value.resetQuery()
    currentTab.value = tabItem
    if (tabItem.menuId) {
      getTabPermissionGroup(tabItem.menuId)
    }
    if (tabItem.meta?.uri && tabItem.meta?.uri == 'tms/company/switch/config/loadVin/detailById') {
      getConfigurableData(currentTab.value.meta?.uri)
    } else {
      if (currentItem.value?.id && tabItem.meta?.uri) {
        requestUri.value = tabItem.meta?.uri
        getBottomTableList(currentItem.value?.id, tabItem.meta?.uri, true)
      }
    }
  }
  /**
   * 批量删除右下区域列表数据
   * @param ids
   */
  const handleDeleteBottomListItem = (ids: string) => {
    batchDeleteApi(ids, currentTab.value?.meta?.uri!)
      .then(() => {
        ElMessage.success('删除成功')
        getBottomTableList(currentItem.value?.id!, currentTab.value?.meta?.uri!)
      })
      .catch((err) => {
        // ElMessage.error(err);
      })
  }
  /**
   * 分页查询底部列表数据
   */
  const handleQueryBottomList = (params: any) => {
    // bottomListQueryParams.limit = params.limit;
    // bottomListQueryParams.page = params.page;
    //遍历params
    for (const key in params) {
      if (params.hasOwnProperty(key)) {
        bottomListQueryParams[key] = params[key]
      }
    }

    getBottomTableList(currentItem.value?.id!, currentTab.value?.meta?.uri!)
  }
  /**
   * 新增
   * @param position 按钮位置
   * @param type
   */
  const addItem = async (position: string, type: string) => {
    buttonPosition.value = position
    dialog.visible = true
    isEdit.value = false
    dialog.title = '新增'
    let form: MenuVO = btnGroup.value.find((item: MenuVO) => item.meta?.purpose === type) || ({} as MenuVO)
    requestUri.value = form.meta!.uri! ?? currentMenuUri.value!
    if (form.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(form.meta!.dataColumn)
    } else {
      operationColumn.value = await refreshFormSelectOptions(rightTopRef.value.tableConfig.tableItem)
    }
  }
  /**
   * 新增线路
   * @param position 按钮位置
   * @param type
   */
  const addLineFun = async (position: string, type: string) => {
    buttonPosition.value = position
    addLineDialog.visible = true
    isEdit.value = false
    addLineDialog.title = '新增'
    let form: MenuVO = btnGroup.value.find((item: MenuVO) => item.meta?.purpose === type) || ({} as MenuVO)
    requestUri.value = form.meta!.uri! ?? currentMenuUri.value!
    if (form.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(form.meta!.dataColumn)
    } else {
      operationColumn.value = await refreshFormSelectOptions(rightTopRef.value.tableConfig.tableItem)
    }
  }
  /**
   * 新增联运线路
   */
  const addSubLine = async (position: string, type: string) => {
    subLineDialog.visible = true
    subLineDialog.title = '新增'
  }
  /**
   *
   * @param position
   * @param type
   * @param btnItem
   */
  const addBottomItem = async (position: string, type: string, btnItem: any) => {
    buttonPosition.value = position
    dialog.visible = true
    isEdit.value = false
    dialog.title = '新增'
    requestUri.value = currentTabUri.value!
    // let form: MenuVO = btnGroup.value.find((item: MenuVO) => item.meta?.purpose === type) || {};
    operationColumn.value = rightBottomRef.value.tableConfig.tableItem
    if (btnItem.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(btnItem.meta!.dataColumn)
    } else {
      operationColumn.value = await refreshFormSelectOptions(rightBottomRef.value.tableConfig.tableItem)
    }
    requestUri.value = btnItem.meta!.uri ?? currentTabUri.value
  }
  /**Data
   * 编辑
   * @param position 按钮位置
   * @param type
   */
  const editItem = async (position: string, type: string) => {
    buttonPosition.value = position
    dialog.visible = true
    isEdit.value = true
    dialog.title = '修改'
    //初始化表单
    const initFormData = resetFormGlobalFun(rightTopRef.value.tableConfig.tableItem!)
    let form: MenuVO = btnGroup.value.find((item: MenuVO) => item.meta?.purpose === type) || ({} as MenuVO)
    if (form.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(form.meta!.dataColumn)
    } else {
      operationColumn.value = await refreshFormSelectOptions(rightTopRef.value.tableConfig.tableItem)
    }
    //按钮上有uri就用按钮的没有用全局的
    requestUri.value = form.meta!.uri! ?? currentMenuUri.value!
    nextTick(() => {
      Object.assign(formDialogRef.value.formData, initFormData)
      Object.assign(formDialogRef.value.formData, currentItem.value)
    })
  }
  /**
   * 修改底部列表项
   * @param row 当前行数据
   * @param position 按钮位置
   */
  const editBottomItem = async (row: TableItem, position: string) => {
    dialog.visible = true
    isEdit.value = true
    dialog.title = '修改'
    buttonPosition.value = position
    //初始化表单
    const initFormData = resetFormGlobalFun(rightBottomRef.value.tableConfig.tableItem!)
    let form: MenuVO = bottomButtomPermissionGroup.value.find((item: MenuVO) => item.meta?.purpose === 'topEdit') || ({} as MenuVO)
    if (form.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(form.meta!.dataColumn)
      requestUri.value = form.meta!.uri! ?? currentMenuUri.value!
    } else {
      requestUri.value = currentTabUri.value! ?? currentMenuUri.value!
      operationColumn.value = await refreshFormSelectOptions(rightBottomRef.value.tableConfig.tableItem)
    }
    nextTick(() => {
      Object.assign(formDialogRef.value.formData, initFormData)
      Object.assign(formDialogRef.value.formData, row)
    })
  }
  /**
   * 删除
   */
  const handleDelete = () => {
    deleteItemApi(currentItem.value?.id!, currentMenuUri.value!).then(() => {
      ElMessage.success('删除成功')
      getList()
    })
  }
  /**
   * 待配板划分设置
   */
  // const waitingAllocateVinWarning = (position?: string, purpose?: string, menu?: MenuVO) => {
  //   waitingProjectDialogRef.value.waitingProjectDialogVisible.visible = true;
  //   waitingProjectDialogRef.value.waitingProjectDialogVisible.title = menu?.meta.title;
  //   globalRequestUrlApi({ id: currentItem.value?.id! }, menu?.meta.form?.method!, menu?.meta?.uri!).then(async res => {
  //     if (res) {
  //       nextTick(() => {
  //         setupData.value = res;
  //       });
  //     } else {
  //       setupData.value = {};
  //     }
  //   });
  // };

  /**
   * 表单提交
   * @param formData 表单
   * @param uri 请求地址前缀
   */
  const handleSubmit = useThrottleFn((formData: CustomerVO, uri: string) => {
    const id = formData.id
    if (!formData.relationId) {
      formData.relationId = currentItem.value?.id
    }
    if (id) {
      updateItemApi(id, formData, uri)
        .then(() => {
          Reflect.deleteProperty(formData, 'id')
          ElMessage.success('修改成功')
          formDialogRef.value.resetForm()
          closeDialog()
          //如果是顶部按钮，则刷新客户列表
          if (buttonPosition.value === 'listTop') {
            getList()
          } else {
            //如果是tab下的按钮，则刷新tab下的列表
            //默认获取当前客户下的用户列表
            getBottomTableList(currentItem.value?.id!, currentTabUri.value!)
          }
          clearFormColumn()
        })
        .finally(() => {})
    } else {
      addItemApi(formData, uri)
        .then(() => {
          ElMessage.success('新增成功')
          formDialogRef.value.resetForm()
          closeDialog()
          //如果是顶部按钮，则刷新客户列表
          if (buttonPosition.value === 'listTop') {
            getList()
          } else {
            //如果是tab下的按钮，则刷新tab下的列表
            //默认获取当前客户下的用户列表
            getBottomTableList(currentItem.value?.id!, currentTabUri.value!)
          }
          clearFormColumn()
        })
        .finally(() => {})
    }
  }, 3000)
  const subLineFormRef = ref()
  //新增子联运线路
  const handleSubLineSubmit = async () => {
    const params = {
      id: currentItem.value?.id,
      subLineIds: subLineFormRef.value.formData.tableData1.map((item: any) => item.id),
    }
    await postAddSubLineApi(params)
    ElMessage.success('创建成功')
    subLineDialog.visible = false
    //默认获取当前客户下的用户列表
    getBottomTableList(currentItem.value?.id!, currentTabUri.value!)
  }
  /**
   * 状态查询
   * @param enable 启用禁用
   */
  const changeStatus = (enable: boolean) => {
    leftListQueryParams.enable = enable
    getList()
  }
  /**
   * 导出Excel
   */
  const exportExcelFun = useThrottleFn((position: string, type: string, item: MenuVO) => {
    let requestApi = item.meta?.uri ? globalExportExcel : exportExcel
    requestApi(queryParams, item.meta?.uri ?? currentMenuUri.value!)
      .then(async (res) => {
        exportButtonLoading.value = true
        await downloadFileGlobalFun(res)
        exportButtonLoading.value = false
        ElMessage.success('操作成功')
      })
      .catch(() => {
        exportButtonLoading.value = false
      })
  }, 3000)
  /**
   * 导出底部tab列表Excel
   * @param purpose 按钮类型
   * @param position 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   * @param menu 按钮信息
   */
  const exportBottomTabListExcel = useThrottleFn((purpose: string, position?: string, menu?: MenuVO) => {
    let requestApi = menu!.meta!.uri ? globalExportExcel : exportExcel
    requestApi(bottomListQueryParams, menu!.meta!.uri ?? currentMenuUri.value!)
      .then(async (res) => {
        exportButtonLoading.value = true
        await downloadFileGlobalFun(res)
        exportButtonLoading.value = false
        ElMessage.success('操作成功')
      })
      .catch(() => {
        exportButtonLoading.value = false
      })
  }, 3000)
  /**
   * 下载导入模版
   * @param purpose 按钮类型
   * @param position 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   * @param menu 按钮信息
   */
  const downloadExcelTemplate = useThrottleFn((purpose: string, position?: string, menu?: MenuVO) => {
    const requestApi = menu?.meta?.uri ? downloadTemplate : globalDownloadTemplate
    requestApi(menu?.meta?.uri ?? currentMenuUri.value!)
      .then(async (res) => {
        downloadButtonLoading.value = true
        await downloadFileGlobalFun(res)
        downloadButtonLoading.value = false
        ElMessage.success('操作成功')
      })
      .catch(() => {
        downloadButtonLoading.value = false
      })
  }, 3000)
  /**
   * 下载导入模版
   */
  const downloadButtomTabExcelTemplate = useThrottleFn((purpose: string, position?: string, menu?: MenuVO) => {
    const requestApi = menu?.meta?.uri ? downloadTemplate : globalDownloadTemplate
    requestApi(menu?.meta?.uri ?? currentMenuUri.value!)
      .then(async (res) => {
        downloadButtonLoading.value = true
        await downloadFileGlobalFun(res)
        downloadButtonLoading.value = false
        ElMessage.success('操作成功')
      })
      .catch(() => {
        downloadButtonLoading.value = false
      })
  }, 3000)
  /**
   * 导入Excel
   * @param file 文件
   */
  const importExcelFun = useThrottleFn((file: any, meta?: MetaVO) => {
    importButtonLoading.value = true
    const Api = meta?.uri ? importFileGlobalBtnUriFun : importFileGlobalFun
    Api(file, meta?.uri ?? currentMenuUri.value!)
      .then(async (res) => {
        ElMessage.success('导入成功')
        importButtonLoading.value = false
        await getList()
      })
      .catch(() => {
        importButtonLoading.value = false
      })
  }, 3000)
  /**
   * 导入Excel
   * @param file 文件
   */
  const importButtomTabExcelFun = useThrottleFn((file: any, meta?: MetaVO) => {
    importButtonLoading.value = true
    const Api = meta?.uri ? importFileGlobalBtnUriFun : importFileGlobalFun
    Api(file, meta?.uri ?? currentTabUri.value!)
      .then(async (res) => {
        importButtonLoading.value = true
        ElMessage.success('导入成功')
        getBottomTableList(currentItem.value?.id!, currentTabUri.value!)
      })
      .catch(() => {
        importButtonLoading.value = true
      })
  }, 3000)
  /**
   * 筛选条件查询
   * @param searchParams 查询条件
   */
  const handleSearchQuery = (searchParams: any) => {
    for (const key in searchParams) {
      leftListQueryParams[key] = searchParams[key]
    }
    getList()
  }

  const getConfigurableData = (currentUri: any) => {
    if (currentItem.value?.id) {
      rightBottomRef.value.listLoading = true
      globalRequestUrlApi({ id: currentItem.value?.id! }, 'get', currentUri!)
        .then(async (res) => {
          if (res && res.data) {
            detailData.value = res.data
          } else {
            detailData.value = {}
          }
          sessionStorage.setItem('configDetail', JSON.stringify(detailData.value))
          rightBottomRef.value.listLoading = false
        })
        .catch(() => {
          rightBottomRef.value.listLoading = false
        })
    } else {
      detailData.value = {}
    }
  }

  const deleteItemFun = (row: any) => {
    deleteItemApi(row.id!, requestUri.value!).then((res: any) => {
      ElMessage.success(res.message)
      getList()
    })
  }
  //弹窗显示菜单
  const showRowMenuDialog = (query: any, column: TableItem) => {
    detailDialogVisible.visible = true
    var title = column.label
    if (column.jump) {
      title = column.jump!.title ?? column.label
      if (column.jump?.dialogWidth) {
        detailDialogVisible.dialogWidth = column.jump!.dialogWidth ?? '80%'
      }
    }
    detailDialogVisible.title = title
  }
  /**
   * 获取表单项，用于表单中级联操作
   * @param formData 表单项
   * @param dependOn 当前修改的依赖项下拉
   */
  const getFormData = async (formData: any, dependOn: string) => {
    setTimeout(async () => {
      //获取form表单数据列
      const newOperationColumn = await getSelectOptions(operationColumn.value, formData, dependOn, 'formSelect')
    }, 200)
  }
  /**
   *  列表右侧按钮默认操作
   * @param row
   * @param position 按钮位置
   * @param menu
   */
  const defaultHandleTable = async (row: TableItem, position: string, menu: MenuVO) => {
    btnMenu.value = menu
    requestUri.value = menu.meta!.uri! ?? requestUri.value!
    isEdit.value = menu.meta?.purpose === 'edit' || menu.meta.position!.indexOf('Right') > -1
    if (menu.meta?.form?.formUri) {
      //定义传递的参数
      let params = {} as any
      const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
      menu.meta?.form?.params?.map((_item) => {
        composeRequestParams(params, _item, menu, storeDataParams, row, null)
      })
      if (menu.meta?.form?.method === 'get' || menu.meta?.form?.method === 'GET') {
        globalRequestUrlApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      } else {
        globalRequestApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      }
    } else {
      dialog.visible = true
      if (menu.meta!.dataColumn.length > 0) {
        operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn)
      } else {
        operationColumn.value = await refreshFormSelectOptions(tableConfig.tableItem)
      }
      await globalBtnForm(row, position, menu)
      dialog.title = menu.meta!.title
    }
    //如果menu.meta?.form?.refreshPage==true ,则刷新当前列表
    if (menu.meta?.form?.refreshPage) await getList()
    //如果menu.meta?.form?.refreshMenuCount==true ,则更新菜单上的数字
    if (menu.meta?.form?.refreshMenuCount) refreshMenuCount()
    //如果menu.meta?.form?.trigerUris存在，遍历menu.meta?.form?.trigerUris，请求相应接口
    if (menu.meta?.form?.trigerUris && menu.meta?.form?.trigerUris.length > 0) {
      menu.meta?.form?.trigerUris?.map((item) => {
        //定义传递的参数
        let params = {} as any
        item.params?.map((_item) => {
          composeRequestParams(params, _item, menu, null, row, null)
        })
        if (item.method === 'get' || item.method === 'GET') {
          globalRequestUrlApi(params, item.method!, item.uri!).then(async (res) => {})
        } else {
          globalRequestApi(params, item.method!, item.uri!).then(async (res) => {})
        }
      })
    }
  }
  /**
   * 默认按钮操作
   * @param row
   * @param position
   * @param menu
   */
  const globalBtnForm = async (row: TableItem, position: string, menu: MenuVO) => {
    btnRequestUri.value = menu.meta?.form?.formUri ?? null
    if (menu.meta?.purpose === 'copy') {
      isEdit.value = false
    } else {
      isEdit.value = menu.meta?.purpose === 'edit' || menu.meta.position!.indexOf('Right') > -1
    }
    buttonPosition.value = position //初始化表单
    const initFormData = resetFormGlobalFun(menu.meta?.dataColumn!)
    if (menu.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn)
    } else {
      operationColumn.value = await refreshFormSelectOptions(tableConfig.tableItem)
    }
    // 根据按钮返回数据渲染当前表单，如果是下拉表单 或者是级联表单项，需要将返回数据设置到表单项的option.data上
    //遍历数组operationColumn.value，找到里面的form的type是select，将row中和form的name相同的项赋值他
    if (position === 'listTop' || position === 'listTabTop') {
      operationColumn.value.map((item: TableItem) => {
        if (item?.form?.type === 'select' || item?.form?.type === 'selectTree' || item?.form?.type === 'cascader') {
          if (row[item.form.name!]) {
            if (!item.form!.option!.data) {
              item.form!.option!.data = []
            }
            //如果row[item.form.name!] 类型是数组则赋值给item.form.option!.data
            if (Array.isArray(row[item.form.name!])) {
              item.form!.option!.data = row[item.form.name!]
            }
            delete row[item.form.name!]
          }
        }
      })
    }
    if (position === 'listRight' || position === 'listTabRight') {
      operationColumn.value.map((item: TableItem) => {
        if (item?.form?.type === 'select' || item?.form?.type === 'selectTree' || item?.form?.type === 'cascader') {
          if (row[item!.form.name!]) {
            if (!item!.form!.option!.data) {
              item!.form!.option!.data = []
            }
            // item!.form.option!.data = row[item!.form.name!];
            // delete row[item.form.name!];
          }
        }
      })
    }
    dialog.visible = true
    dialog.title = menu.meta?.title || '操作'
    const deepRow = JSON.parse(JSON.stringify(row))
    //如果是复制，并且operationColumn.value中的每项的form.canCopy为false，则将deepRow中的对应项的value设置为空
    if (menu.meta?.purpose === 'copy') {
      operationColumn.value.forEach((item: TableItem) => {
        if (item.form?.canCopy === false) {
          deepRow[item.form.name!] = null
        }
      })
      deepRow.id = undefined
    }
    await nextTick(() => {
      Object.assign(formDialogRef.value.formData, initFormData)
      Object.assign(formDialogRef.value.formData, deepRow)
    })
  }

  /**
   * 更新菜单上的数字
   */
  function refreshMenuCount() {
    getMenuCount().then((res) => {
      sideBarStore.$patch((state) => {
        state.menuCount = res.data as any
      })
    })
  }

  /**
   * 分页查询左侧菜单列表数据
   */
  const handleQueryLeftSideBarList = (_queryParams: BasePageQuery) => {
    leftListQueryParams.page = _queryParams.page
    leftListQueryParams.limit = _queryParams.limit
    getList()
  }
  /**
   * 左侧菜单模糊搜索
   */
  const leftSideBarListNameSearch = (name: string) => {
    leftListQueryParams.name = name
    getList()
  }
  const { searchParams } = toRaw(formStore)
  // 弹窗菜单
  const showMenuDialog = (position: string, purpose: string, menu: MenuVO) => {
    //获取需要传递的formData中的属性保存到store中
    let mergeDialogFormParams: { [key: string]: any } = {}
    let storeDialogFormParams: { [key: string]: any } = {}
    const storeData = sideBarStore.$state.storeDialogFormParams || {}
    const storeFormData = sideBarStore.$state.mergeDialogFormParams || {}
    if (menu.meta.form?.storeData) {
      for (let item of menu.meta.form?.storeData!) {
        composeRequestParams(storeDialogFormParams, item, null, null, null, searchParams.value)
      }
    }
    let query: { [key: string]: any } = {}
    if (menu.meta.form?.query) {
      for (let item of menu.meta.form?.query!) {
        composeRequestParams(query, item, null, null, null, searchParams.value)
      }
    }
    // router.push({
    //   name: column.jump?.targetField,
    //   query: query,
    // });
    sideBarStore.$patch((state) => {
      state.btnMenuId = menu.meta.form?.menuId!
      state.btnMenuQuery = query
      state.mergeDialogFormParams = Object.assign(storeFormData, mergeDialogFormParams) //获取需要传递的formData中的属性保存到store中
      state.storeDialogFormParams = Object.assign(storeData, storeDialogFormParams) //获取需要传递的全局存储的属性保存到store中
    })
    detailDialogVisible.visible = true
    detailDialogVisible.title = menu.meta.form!.title
  }
  const showRightBottomMenuDialog = async (query: any, form: BtnFormVO) => {
    detailDialogVisible.visible = true
    detailDialogVisible.title = form.title
  }
</script>
<style lang="scss" scoped>
  .statusText {
    overflow: hidden;
  }
</style>
