<!--
 * @Author: llm
 * @Date: 2023-06-10 12:27:32
 * @LastEditors: llm
 * @LastEditTime: 2025-07-29 15:12:42
 * @Description: 仓库管理
 *
-->
<template>
  <div class="app-container">
    <!-- 顶部搜索区域 -->
    <el-card shadow="never" style="margin-bottom: 20px" class="top-query">
      <topQueryGroupComponent :query-permission-group="topQueryConfig.tableItem" @handleSearchQuery="handleSearchQuery" />
    </el-card>
    <el-card>
      <template #header>
        <!-- 按钮组 -->
        <button-group-component
          ref="topButtonGroupComponent"
          :ids="customerIds"
          :exportButtonLoading="exportButtonLoading"
          :buttonPermissionGroup="buttonPermissionGroup"
          @addItem="addItem"
          @editItem="editItem"
          @handleDelete="handleDelete"
          @exportExcel="exportExcelFun"
          @downloadExcelTemplate="downloadExcelTemplate"
          @importExcel="importExcelFun"
        />
      </template>
      <div>
        <el-row>
          <el-col :span="4">
            <!-- 左侧栏 -->
            <div class="left-sidebar">
              <left-sidebar-component
                ref="leftSideBarListRef"
                :leftSideBarList="leftSideBarList"
                :listName="listName"
                :checkedItem="checkedItem"
                @getItem="getItem"
                @changeStatus="changeStatus"
              />
            </div>
          </el-col>
          <el-col :span="20">
            <!-- 右侧区域 -->
            <div>
              <div>
                <right-top-component
                  ref="rightTopRef"
                  :descriptionsConfig="tableConfig.tableItem"
                  :info="currentItem"
                  @handleStatusChange="handleStatusChange"
                />
              </div>
              <div>
                <right-bottom-component
                  ref="rightBottomRef"
                  :bottomTabPermissionGroup="bottomTabPermissionGroup"
                  :bottomButtomPermissionGroup="bottomButtomPermissionGroup"
                  :requestUri="btnRequestUri ?? requestUri!"
                  @changeTab="changeTab"
                  @handleDeleteBottomListItem="handleDeleteBottomListItem"
                  @handleQueryBottomList="handleQueryBottomList"
                  @addItem="addBottomItem"
                  @editBottomItem="editBottomItem"
                  @switchBottomListStatus="switchBottomListStatus"
                  @exportBottomTabListExcel="exportBottomTabListExcel"
                  @downloadButtomTabExcelTemplate="downloadButtomTabExcelTemplate"
                  @importButtomTabExcelFun="importButtomTabExcelFun"
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
    <!-- 新增弹窗 -->
    <form-dialog
      v-if="dialog.visible"
      ref="formDialogRef"
      :dialog="dialog"
      :is-edit="isEdit"
      :dataColumn="operationColumn"
      :requestUri="requestUri"
      @closeDialog="closeDialog"
      @handleSubmit="handleSubmit"
      @clearFormColumn="clearFormColumn"
    />
  </div>
</template>
<script setup lang="ts">
  defineOptions({
    name: 'WarehouseBaseData',
    inheritAttrs: false,
  })
  import buttonGroupComponent from '@/components/TopButtonGroupComponent/index.vue' //操作按钮
  import formDialog from '@/components/FormDialogComponent/index.vue' //表单弹窗
  import leftSidebarComponent from '../components/leftSidebarComponent.vue' //左侧栏
  import rightTopComponent from '../components/rightTopComponent.vue' //右上部
  import rightBottomComponent from '../components/rightBottomComponent.vue' //右下部
  import { CustomerVO, UserQuery } from '@/api/customerCenter/customerBaseData/types'
  import { downloadFileGlobalFun, getSelectOptions, getcurrentUserMenuColumnlist, resetFormGlobalFun, switchChangeGlobalFun } from '@/utils/common'
  import { UserForm } from '@/api/authorityManagement/CorporateAccountManagement/PersonalManagement/types'
  import {
    deleteItemApi,
    downloadTemplate,
    exportExcel,
    importFileGlobalFun,
    getListAll,
    bottomTableList,
    updateItemApi,
    addItemApi,
    batchDeleteApi,
    globalExportExcel,
    globalDownloadTemplate,
  } from '@/api/auth'
  import { FormColumn } from '@/types/global'
  import defaultSettings from '@/settings'

  /**
   * 按钮下的表单
   */
  const operationColumn = ref<any>()

  /**
   * 按钮的请求地址前缀
   */
  const btnRequestUri = ref<string | null>()
  /**
   * 获取当前菜单的menuId
   */
  const { proxy }: any = getCurrentInstance()
  /**
   * 是否编辑状态
   */
  const isEdit = ref<boolean>()
  /**
   * 按钮组
   */
  const btnGroup = ref<MenuVO[]>([])
  /**
   * 客户列表查询条件
   */
  const queryParams = reactive<any>({ page: 1, limit: defaultSettings.globalLimit })
  /**
   * 底部列表查询条件
   */
  const bottomListQueryParams = reactive<UserQuery>({
    page: 1,
    limit: defaultSettings.globalLimit,
  })
  const topButtonGroupComponent = ref()
  /**
   * 左侧选中列表项
   */
  const checkedItem = ref<string[]>()
  /**
   * form 表单弹窗
   */
  const formDialogRef = ref()
  /**
   * 按钮组权限
   */
  const buttonPermissionGroup = ref<MenuVO[]>()
  /**
   * 右下区域tab
   */
  const bottomTabPermissionGroup = ref<MenuVO[]>([])
  /**
   * 右下区域tab中的按钮组
   */
  const bottomButtomPermissionGroup = ref<MenuVO[]>([])
  /**
   * 左侧栏列表
   */
  const leftSideBarListRef = ref()
  /**
   * 右上区域
   */
  const rightTopRef = ref()
  /**
   * 右下区域
   */
  const rightBottomRef = ref()
  /**
   * 选中的tab
   */
  const currentTab = ref<MenuVO>()
  /**
   * 选中的当前行
   */
  const currentItem = ref<TableItem>()
  /**
   * 左侧列表
   */
  const leftSideBarList = ref<CustomerVO[]>([])
  /**
   * 用于显示在左侧栏中的列表名称
   */
  const listName = '仓库'
  /**
   * 勾选需要删除的客户ids
   */
  const customerIds = ref<string[]>()
  /**
   * 当前菜单的uri
   */
  const currentMenuUri = ref<string>()
  /**
   * 当前tab的uri
   */
  const currentTabUri = ref<string>()
  /**
   * 请求地址前缀
   */
  const requestUri = ref<string>()
  /**
   * 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   */
  const buttonPosition = ref<string>()
  /**
   * 导出加载动画
   */
  const exportButtonLoading = ref<boolean>()
  /**
   * 弹窗
   */
  const dialog = reactive<DialogOption>({
    visible: false,
  })
  /**
   * meta
   */
  const metaInfo = ref<MetaVO>()
  /**
   * 列表配置项
   */
  const tableConfig = reactive<TableConfig>({
    showHandleSelection: true,
    showSort: true,
    tableItem: [],
    operation: {
      label: '操作',
      items: [],
      width: '120px',
    },
  })
  /**
   * 初始化表单数据
   */
  const clearFormColumn = () => {
    operationColumn.value = []
    dialog.title = ''
  }
  /**
   * 顶部搜索配置项
   */
  const topQueryConfig = reactive<TableConfig>({ tableItem: [] })
  onMounted(async () => {
    await getPermission(proxy.$sideBarStore.$state.menuId)
    await getList()
  })
  /**
   * 获取菜单下的权限
   * @param menuId 菜单id
   */
  const getPermission = async (menuId: string) => {
    return new Promise<void>(async (resolve, reject) => {
      const { children, meta }: { children: MenuVO[]; meta: MetaVO } = await getcurrentUserMenuColumnlist(menuId)
      metaInfo.value = meta
      btnGroup.value = children
      //获取顶部按钮组
      buttonPermissionGroup.value = children.filter((item: MenuVO) => item.meta?.type !== 3)
      //获取右下区域tab组
      bottomTabPermissionGroup.value = children.filter((item: MenuVO) => item.meta?.type === 3)
      //如果bottomTabPermissionGroup.length>0,取第一个tab的menuId,获取数据列
      if (bottomTabPermissionGroup.value.length > 0 && bottomTabPermissionGroup.value[0].menuId) {
        //默认当前tab为tab中的第一项
        currentTab.value = bottomTabPermissionGroup.value[0]
        await getTabPermissionGroup(bottomTabPermissionGroup.value[0].menuId)
      }
      //获取form表单数据列
      const dataColumn: any = await getSelectOptions(meta.dataColumn)
      currentMenuUri.value = meta.uri
      topQueryConfig.tableItem = tableConfig.tableItem = rightTopRef.value.tableConfig.tableItem = dataColumn
      resolve()
    })
  }
  /**
   * 切换tab 重新获取权限
   * @param menuId 菜单id
   */
  const getTabPermissionGroup = async (menuId: string) => {
    const { children, meta }: { children: MenuVO[]; meta: MetaVO } = await getcurrentUserMenuColumnlist(menuId)
    bottomButtomPermissionGroup.value = children
    currentTabUri.value = meta.uri
    //获取form表单数据列
    const dataColumn: any = await getSelectOptions(meta.dataColumn)
    rightBottomRef.value.tableConfig.tableItem = dataColumn
    rightBottomRef.value.tableConfig.operation = meta.operation
  }
  /**
   * 获取左侧数据列表
   */
  const getList = async () => {
    leftSideBarListRef.value.listTreeLoading = true
    getListAll(queryParams, currentMenuUri.value!)
      .then((res) => {
        const { data } = res
        leftSideBarList.value = data
        if (data.length > 0 && data[0].id) {
          //默认customerIds为客户列表的第一条数据id
          customerIds.value = [data[0].id]
          //默认获取当前第一条数据
          currentItem.value = data[0]
          if (bottomTabPermissionGroup.value.length > 0 && bottomTabPermissionGroup.value[0].meta?.uri) {
            requestUri.value = bottomTabPermissionGroup.value[0].meta?.uri
            //默认获取当前客户下的用户列表
            getBottomTableList(data[0].id, currentTabUri.value!)
          }
        } else {
          currentItem.value = {}
          rightBottomRef.value.tableData = []
          rightBottomRef.value.total = 0
          rightBottomRef.value.listLoading = false
        }
        leftSideBarListRef.value.listTreeLoading = false
        refreshQuerySelectOptions()
      })
      .catch((err) => {
        leftSideBarListRef.value.listTreeLoading = false
      })
  }
  /**
   * 更新查询条件下拉
   */
  const refreshQuerySelectOptions = async () => {
    //重新获取form表单数据列，刷新筛选下拉项
    const dataColumn: any = await getSelectOptions(metaInfo.value!.dataColumn, null, '', 'topQuerySelect')
    topQueryConfig.tableItem = dataColumn
  }
  /**
   * 更新表单下拉
   */
  const refreshFormSelectOptions = async (dataColumn: FormColumn[]) => {
    //重新获取form表单数据列，刷新筛选下拉项
    return new Promise(async (resolve, reject) => {
      const newDataColumn = getSelectOptions(dataColumn, null, '', 'formSelect')
      resolve(newDataColumn)
    })
  }
  /**
   * 查询选中的客户详情
   */
  const getItem = async (row: TableItem) => {
    currentItem.value = row
    //ids 赋值 为当前客户id
    customerIds.value = [row.id!]
    if (currentTab.value?.menuId) {
      await getTabPermissionGroup(currentTab.value?.menuId)
    }
    if (bottomTabPermissionGroup.value.length > 0 && bottomTabPermissionGroup.value[0].meta?.uri && row.id) {
      //默认获取当前客户下的用户列表
      getBottomTableList(row.id, currentTab.value?.meta ? currentTab.value?.meta?.uri! : bottomTabPermissionGroup.value[0].meta?.uri)
    }
  }

  /**
   * 关闭弹窗
   */
  function closeDialog() {
    dialog.visible = false
    resetForm()
  }

  /**
   * 重置表单
   */
  function resetForm() {
    formDialogRef.value.resetForm()
  }

  /**
   * 修改侧边栏列表状态
   */
  const handleStatusChange = async (row: { [key: string]: any }) => {
    await switchChangeGlobalFun(row.enable, row.id, currentMenuUri.value!)
      .then(async () => {
        //修改成功后刷新列表
        await getList()
      })
      .catch(() => {
        //取消切换，恢复状态
        row.enable = row.enable !== true
      })
  }
  /**
   * 启用禁用底部列表项状态
   */
  const switchBottomListStatus = async (row: UserForm) => {
    await switchChangeGlobalFun(row.enable!, row.id, currentTabUri.value!)
      .then(async () => {
        //修改成功后刷新列表
        getBottomTableList(currentItem.value?.id!, currentTabUri.value!)
      })
      .catch(() => {
        //取消切换，恢复状态
        row.enable = row.enable !== true
      })
  }
  /**
   * 获取客户下的用户列表
   * @param relationId 客户id
   * @param uri 请求地址
   */
  const getBottomTableList = (relationId: string, uri: string) => {
    rightBottomRef.value.listLoading = true
    bottomListQueryParams.relationId = relationId
    bottomTableList(bottomListQueryParams, uri)
      .then((res) => {
        const { data } = res
        rightBottomRef.value.tableData = data.rows
        rightBottomRef.value.total = data.total
        rightBottomRef.value.listLoading = false
      })
      .catch((err) => {
        rightBottomRef.value.listLoading = false
      })
  }
  /**
   * 切换tab
   * @param tabItem
   */
  const changeTab = (tabItem: MenuVO) => {
    //初始化page
    bottomListQueryParams.page = 1
    rightBottomRef.value.listQueryParams.page = 1
    currentTab.value = tabItem
    if (tabItem.menuId) {
      getTabPermissionGroup(tabItem.menuId)
    }
    if (currentItem.value?.id && tabItem.meta?.uri) {
      requestUri.value = tabItem.meta?.uri
      getBottomTableList(currentItem.value?.id, tabItem.meta?.uri)
    }
  }
  /**
   * 批量删除右下区域列表数据
   * @param ids
   */
  const handleDeleteBottomListItem = (ids: string) => {
    batchDeleteApi(ids, currentTab.value?.meta?.uri!).then(() => {
      ElMessage.success('删除成功')
      getBottomTableList(currentItem.value?.id!, currentTab.value?.meta?.uri!)
    })
  }
  /**
   * 分页查询底部列表数据
   */
  const handleQueryBottomList = (params: UserQuery) => {
    bottomListQueryParams.limit = params.limit
    bottomListQueryParams.page = params.page

    getBottomTableList(currentItem.value?.id!, currentTab.value?.meta?.uri!)
  }
  /**
   * 新增
   * @param position 按钮位置
   */
  const addItem = async (position: string, type: string) => {
    buttonPosition.value = position
    dialog.visible = true
    isEdit.value = false
    dialog.title = '新增'
    let form: MenuVO = btnGroup.value.find((item: MenuVO) => item.meta?.purpose === type) || ({} as MenuVO)
    requestUri.value = form.meta!.uri ?? currentMenuUri.value
    if (form.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(form.meta!.dataColumn)
    } else {
      operationColumn.value = await refreshFormSelectOptions(rightTopRef.value.tableConfig.tableItem)
    }
  }
  const addBottomItem = async (position: string, type: string) => {
    buttonPosition.value = position
    dialog.visible = true
    isEdit.value = false
    dialog.title = '新增'
    requestUri.value = currentTabUri.value
    let form: MenuVO = btnGroup.value.find((item: MenuVO) => item.meta?.purpose === type) || ({} as MenuVO)
    operationColumn.value = rightBottomRef.value.tableConfig.tableItem
    if (form.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(rightBottomRef.value.tableConfig.tableItem)
      requestUri.value = form.meta!.uri
    } else {
      requestUri.value = currentTabUri.value
      // btnRequestUri.value = ""; //置空，默认用requestUri
      operationColumn.value = await refreshFormSelectOptions(rightBottomRef.value.tableConfig.tableItem)
    }
  }
  /**
   * 编辑
   * @param position 按钮位置
   */
  const editItem = async (position: string, type: string) => {
    buttonPosition.value = position

    //初始化表单
    const initFormData = resetFormGlobalFun(rightTopRef.value.tableConfig.tableItem!)
    let form: MenuVO = btnGroup.value.find((item: MenuVO) => item.meta?.purpose === type) || ({} as MenuVO)
    if (form.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(form.meta!.dataColumn)
      requestUri.value = form.meta!.uri
    } else {
      requestUri.value = currentMenuUri.value
      // btnRequestUri.value = ""; //置空，默认用requestUri
      operationColumn.value = await refreshFormSelectOptions(rightTopRef.value.tableConfig.tableItem)
    }
    dialog.visible = true
    isEdit.value = true
    dialog.title = '修改'
    await nextTick(() => {
      Object.assign(formDialogRef.value.formData, initFormData)
      Object.assign(formDialogRef.value.formData, currentItem.value)
    })
  }
  /**
   * 修改底部列表项
   * @param row 当前行数据
   * @param position 按钮位置
   */
  const editBottomItem = async (row: TableItem, position: string) => {
    dialog.visible = true
    isEdit.value = true
    dialog.title = '修改'
    buttonPosition.value = position
    //初始化表单
    const initFormData = resetFormGlobalFun(rightBottomRef.value.tableConfig.tableItem!)
    let form: MenuVO = bottomButtomPermissionGroup.value.find((item: MenuVO) => item.meta?.purpose === 'edit') || ({} as MenuVO)
    if (form.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(form.meta!.dataColumn)
      requestUri.value = form.meta!.uri
    } else {
      requestUri.value = currentTabUri.value
      operationColumn.value = await refreshFormSelectOptions(rightBottomRef.value.tableConfig.tableItem)
    }
    nextTick(() => {
      Object.assign(formDialogRef.value.formData, initFormData)
      Object.assign(formDialogRef.value.formData, row)
    })
  }
  /**
   * 删除
   */
  const handleDelete = () => {
    deleteItemApi(currentItem.value?.id!, currentMenuUri.value!).then(() => {
      ElMessage.success('删除成功')
      getList()
    })
  }

  /**
   * 表单提交
   * @param formData 表单
   * @param uri 请求地址前缀
   */
  const handleSubmit = useThrottleFn((formData: CustomerVO, uri: string) => {
    const id = formData.id
    if (!formData.relationId) {
      formData.relationId = currentItem.value?.id
    }
    if (id) {
      updateItemApi(id, formData, uri)
        .then(() => {
          Reflect.deleteProperty(formData, 'id')
          ElMessage.success('修改成功')
          formDialogRef.value.resetForm()
          closeDialog()
          //如果是顶部按钮，则刷新客户列表
          if (buttonPosition.value === 'listTop') {
            getList()
          } else {
            //如果是tab下的按钮，则刷新tab下的列表
            //默认获取当前客户下的用户列表
            getBottomTableList(currentItem.value?.id!, currentTabUri.value!)
          }
          clearFormColumn()
        })
        .finally(() => {})
    } else {
      addItemApi(formData, uri)
        .then(() => {
          ElMessage.success('新增成功')
          formDialogRef.value.resetForm()
          closeDialog()
          //如果是顶部按钮，则刷新客户列表
          if (buttonPosition.value === 'listTop') {
            getList()
          } else {
            //如果是tab下的按钮，则刷新tab下的列表
            //默认获取当前客户下的用户列表
            getBottomTableList(currentItem.value?.id!, currentTabUri.value!)
          }
          clearFormColumn()
        })
        .finally(() => {})
    }
  }, 3000)
  /**
   * 状态查询
   * @param enable 启用禁用
   */
  const changeStatus = (enable: boolean) => {
    queryParams.enable = enable
    getList()
  }
  /**
   * 导出Excel
   */
  const exportExcelFun = useThrottleFn((position: string, type: string, item: MenuVO) => {
    let requestApi = item.meta?.uri ? globalExportExcel : exportExcel
    requestApi(queryParams, item.meta?.uri ?? currentMenuUri.value!).then(async (res) => {
      exportButtonLoading.value = true
      await downloadFileGlobalFun(res)
      exportButtonLoading.value = false
      ElMessage.success('操作成功')
    })
  }, 3000)
  /**
   * 导出底部tab列表Excel
   */
  const exportBottomTabListExcel = useThrottleFn((item: MenuVO) => {
    let requestApi = item.meta?.uri ? globalExportExcel : exportExcel
    requestApi(queryParams, item.meta?.uri ?? currentMenuUri.value!)
      .then(async (res) => {
        exportButtonLoading.value = true
        await downloadFileGlobalFun(res)
        exportButtonLoading.value = false
        ElMessage.success('操作成功')
      })
      .catch(() => {
        exportButtonLoading.value = false
      })
  }, 3000)
  /**
   * 下载导入模版
   * @param purpose 按钮类型
   * @param position 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   * @param menu 按钮信息
   */
  const downloadExcelTemplate = useThrottleFn((purpose: string, position?: string, menu?: MenuVO) => {
    const requestApi = menu?.meta?.uri ? downloadTemplate : globalDownloadTemplate
    requestApi(menu?.meta?.uri ?? currentMenuUri.value!).then(async (res) => {
      exportButtonLoading.value = true
      await downloadFileGlobalFun(res)
      exportButtonLoading.value = false
      ElMessage.success('操作成功')
    })
  }, 3000)
  /**
   * 下载导入模版
   */
  const downloadButtomTabExcelTemplate = useThrottleFn((purpose: string, position?: string, menu?: MenuVO) => {
    const requestApi = menu?.meta?.uri ? downloadTemplate : globalDownloadTemplate
    requestApi(menu!.meta!.uri ?? currentMenuUri.value!).then(async (res) => {
      exportButtonLoading.value = true
      await downloadFileGlobalFun(res)
      exportButtonLoading.value = false
      ElMessage.success('操作成功')
    })
  }, 3000)
  /**
   * 导入Excel
   * @param file 文件
   */
  const importExcelFun = useThrottleFn((file: any) => {
    importFileGlobalFun(file, currentMenuUri.value!).then(async (res) => {
      ElMessage.success('导入成功')
      getList()
    })
  }, 3000)
  /**
   * 导入Excel
   * @param file 文件
   */
  const importButtomTabExcelFun = useThrottleFn((file: any) => {
    importFileGlobalFun(file, currentTabUri.value!).then(async (res) => {
      ElMessage.success('导入成功')
      getBottomTableList(currentItem.value?.id!, currentTabUri.value!)
    })
  }, 3000)
  /**
   * 筛选条件查询
   * @param searchParams 查询条件
   */
  const handleSearchQuery = (searchParams: any) => {
    for (const key in searchParams) {
      queryParams[key] = searchParams[key]
    }
    getList()
  }
</script>
